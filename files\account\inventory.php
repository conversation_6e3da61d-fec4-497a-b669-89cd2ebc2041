<?php $zpanel->checkSession(true); ?>
<div class="page-header"><h1>My inventory <small>check your global inventory</small></h1></div>

<div class="row">
    <div class="panel panel-default">
        <div class="panel-body" style="padding-bottom: 0;">
            <div class="col-lg-12 no-padd">
                <?php if (isset($_GET['update']) || $_GET['update'] == 'true') { ?>
                    <div class="alert alert-success flat j_dismiss"><?php echo S_PLR_ACCOUNT_UPDATED; ?></div>
                <?php } ?>
                <table class="table no-margn">
                    <thead>
                        <tr>
                            <th>Weapon Name</th>
                            <th>Quantity</th>
                            <th>Durability</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php
                        // generic function to get page
                        function getPage($stmt, $pageNum, $rowsPerPage) {
                            $offset = ($pageNum - 1) * $rowsPerPage;
                            $rows = array();
                            $i = 0;
                            while (($row = sqlsrv_fetch_array($stmt, SQLSRV_FETCH_NUMERIC, SQLSRV_SCROLL_ABSOLUTE, $offset + $i)) && $i < $rowsPerPage) {
                                array_push($rows, $row);
                                $i++;
                            }
                            return $rows;
                        }

                        // Set the number of rows to be returned on a page.
                        $rowsPerPage = 10;

                        // Define and execute the query.  
                        // Note that the query is executed with a "scrollable" cursor.
                        $sql = "SELECT * FROM UsersInventory WHERE CustomerID = '$getCustomerID' AND ItemID > 100000";

                        $stmt = sqlsrv_query($conn, $sql, array(), array("Scrollable" => 'static'));
                        if (!$stmt)
                            die(print_r(sqlsrv_errors(), true));

                        // Get the total number of rows returned by the query.
                        $rowsReturned = sqlsrv_num_rows($stmt);
                        if ($rowsReturned === false)
                            die(print_r(sqlsrv_errors(), true));
                        elseif ($rowsReturned == 0) {
                            echo W_NOTHING_RETURNED;
                            //exit();
                        } else {
                            /* Calculate number of pages. */
                            $numOfPages = ceil($rowsReturned / $rowsPerPage);
                        }

                        // Display the selected page of data.
                        $pageNum = isset($_GET['pageNum']) ? $_GET['pageNum'] : 1;
                        $page = getPage($stmt, $pageNum, $rowsPerPage);

                        foreach ($page as $row) {
                            $selectWeapons = "SELECT * FROM Items_Weapons WHERE ItemID = '$row[4]' AND Category <= '26'";
                            $selectWeaponsQuery = sqlsrv_query($conn, $selectWeapons, array());
                            $selectWeaponsFetch = sqlsrv_fetch_array($selectWeaponsQuery, SQLSRV_FETCH_ASSOC);
                            if($selectWeaponsFetch['ItemID'] > 100000){
                            ?>
                            <tr>
                                <td><?php echo $weaponName = ($selectWeaponsFetch['Name'] ? $selectWeaponsFetch['Name'] : 'UNKNOWN WEAPON'); ?></td>
                                <td><?php echo $row[6]; ?></td>
                                <td><?php echo $durability = ($row[9] == 10000 ? 'BRAND NEW' : $row[9]); ?></td>
                            </tr>
                            <?php
                            }
                        }
                        ?>
                    </tbody>
                </table>
                <div class="row">
                    <div class="col-lg-12 text-center">
                        <ul class="pagination">
                            <?php
                            // Display Previous Page link if applicable.
                            if ($pageNum > 1) {
                                $prevPageLink = "?url=account/inventory&pageNum=" . ($pageNum - 1);
                                echo '<li><a href="' . $prevPageLink . '" aria-label="Previous"><span aria-hidden="true">&laquo;</span></a></li>';
                            }

                            for ($i = 1; $i <= $numOfPages; $i++) {
                                $pageLink = "?url=account/inventory&pageNum=$i";
                                ?>
                                <li><a href="<?php echo $pageLink; ?>"><?php echo $i; ?></a></li>
                                <?php
                            }

                            // Display Next Page link if applicable.
                            if ($pageNum < $numOfPages) {
                                $nextPageLink = "?url=account/inventory&pageNum=" . ($pageNum + 1);
                                echo '<li><a href="' . $nextPageLink . '" aria-label="Next"><span aria-hidden="true">&raquo;</span></a></li>';
                            }

                            sqlsrv_close($conn);
                            ?>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>