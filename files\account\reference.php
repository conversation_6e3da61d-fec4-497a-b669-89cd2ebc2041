<div class="page-header"><h1><?php echo PT_REFERENCE; ?></h1></div>

<div class="row">
    <div class="panel panel-default">
        <div class="panel-body">
            <div class="col-lg-12">
                <h4 class="text-red"><?php echo T_MYLINK; ?></h4>
                <input type="text" class="form-control" value="<?php echo ROOT_WITH_PATH; ?>/register.php?referral=<?php echo $getCustomerID; ?>" readonly="readonly">
                <span class="help-block text-red"><?php echo T_HB_REFERRAL; ?></span>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <div class="panel panel-default">
        <div class="panel-body">
            <div class="col-lg-12">
                <h4 class="text-success"><?php echo T_REWARDS; ?></h4>
                <div class="col-lg-4">
                    <div class="thumbnail">
                        <img src="assets/images/thumb/referral5.png" style="width: 100%; height: 200px; margin: 0; display: block;">
                        <div class="caption">
                            <h3 class="text-red">Invite 5 friends</h3>
                            <p>
                            <ul>
                                <li><strong>+</strong> 15,000 GD</li>
                                <li><strong>+</strong> 2 Blaser R93</li>
                            </ul>
                            </p>
                            <a href="#" data-target="#referral5Modal" data-toggle="modal" class="btn btn-block btn-success"
                            <?php
                            $countReferral5 = "SELECT * FROM Accounts WHERE ReferralID = '$getCustomerID'";
                            $countReferral5Opt = array("Scrollable" => SQLSRV_CURSOR_KEYSET);
                            $countReferral5Query = sqlsrv_query($conn, $countReferral5, array(), $countReferral5Opt);

                            if (sqlsrv_num_rows($countReferral5Query) < 1) {
                                echo ' disabled="disabled"';
                            }
                            ?>   
                               >Get reward!</a>
                        </div>
                    </div>
                </div>

                <div class="col-lg-4">
                    <div class="thumbnail">
                        <img src="assets/images/thumb/referral10.png" style="width: 100%; height: 200px; margin: 0; display: block;">
                        <div class="caption">
                            <h3 class="text-red">Invite 10 friends</h3>
                            <p>
                            <ul>
                                <li><strong>+</strong> 25,000 GD</li>
                                <li><strong>+</strong> 2,000 GC</li>
                                <li><strong>+</strong> 2 AW Magnum</li>
                            </ul>
                            </p>
                            <a href="#" data-target="#referral10Modal" data-toggle="modal" class="btn btn-block btn-success"
                            <?php
                            $countReferral10 = "SELECT * FROM Accounts WHERE ReferralID = '$getCustomerID'";
                            $countReferral10Opt = array("Scrollable" => SQLSRV_CURSOR_KEYSET);
                            $countReferral10Query = sqlsrv_query($conn, $countReferral10, array(), $countReferral10Opt);

                            if (sqlsrv_num_rows($countReferral10Query) < 10) {
                                echo ' disabled="disabled"';
                            }
                            ?>   
                               >Get reward!</a>
                        </div>
                    </div>
                </div>

                <div class="col-lg-4">
                    <div class="thumbnail">
                        <img src="assets/images/thumb/referral25.png" style="width: 100%; height: 200px; margin: 0; display: block;">
                        <div class="caption">
                            <h3 class="text-red">Invite 25 friends</h3>
                            <p>
                            <ul>
                                <li><strong>+</strong> 40,000 GD</li>
                                <li><strong>+</strong> 5,000 GC</li>
                                <li><strong>+</strong> 4 AW Magnum</li>
                                <li><strong>+</strong> 2 Blaser R93</li>
                                <li><strong>+</strong> 2 M107</li>
                            </ul>
                            </p>
                            <a href="#" data-target="#referral25Modal" data-toggle="modal" class="btn btn-block btn-success"
                            <?php
                            $countReferral25 = "SELECT ReferralID FROM Accounts WHERE CustomerID = '$getCustomerID'";
                            $countReferral25Opt = array("Scrollable" => SQLSRV_CURSOR_KEYSET);
                            $countReferral25Query = sqlsrv_query($conn, $countReferral25, array(), $countReferral25Opt);

                            if (sqlsrv_num_rows($countReferral25Query) < 25) {
                                echo ' disabled="disabled"';
                            }
                            ?>
                               >Get reward!</a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <div class="panel panel-default">
        <div class="panel-body">
            <div class="col-lg-12">
                <h4 class="text-success"><?php echo T_MYREFERRAL; ?> <span><small class="text-blue text-bolder">(Total <?php echo $userLogin->countTotalReferral($conn); ?>)</small></span></h4>
                <table class="table no-margn">
                    <thead>
                        <tr>
                            <th>Friend ID</th>
                            <th><?php echo T_EMAIL; ?> (provider hidden)</th>
                            <th><?php echo T_REGISTEREDIN; ?></th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php

                        // generic function to get page
                        function getPage($stmt, $pageNum, $rowsPerPage) {
                            $offset = ($pageNum - 1) * $rowsPerPage;
                            $rows = array();
                            $i = 0;
                            while (($row = sqlsrv_fetch_array($stmt, SQLSRV_FETCH_NUMERIC, SQLSRV_SCROLL_ABSOLUTE, $offset + $i)) && $i < $rowsPerPage) {
                                array_push($rows, $row);
                                $i++;
                            }
                            return $rows;
                        }

                        // Set the number of rows to be returned on a page.
                        $rowsPerPage = 5;

                        // Define and execute the query.  
                        // Note that the query is executed with a "scrollable" cursor.
                        $sql = "SELECT * FROM Accounts WHERE ReferralID = '$getCustomerID' ORDER BY dateregistered DESC";

                        $stmt = sqlsrv_query($conn, $sql, array(), array("Scrollable" => 'static'));
                        if (!$stmt)
                            die(print_r(sqlsrv_errors(), true));

                        // Get the total number of rows returned by the query.
                        $rowsReturned = sqlsrv_num_rows($stmt);
                        if ($rowsReturned === false)
                            die(print_r(sqlsrv_errors(), true));
                        elseif ($rowsReturned == 0) {
                            echo W_NOTHING_RETURNED;
                            //exit();
                        } else {
                            /* Calculate number of pages. */
                            $numOfPages = ceil($rowsReturned / $rowsPerPage);
                        }

                        // Display the selected page of data.
                        $pageNum = isset($_GET['pageNum']) ? $_GET['pageNum'] : 1;
                        $page = getPage($stmt, $pageNum, $rowsPerPage);

                        foreach ($page as $row) {
                            ?>
                            <tr>
                                <td><?php echo $row[0]; ?></td>
                                <td>
                                    <?php
                                    $searchProvider = array("hotmail", "gmail");
                                    $replaceProvider = str_replace($searchProvider, 'xxxxx', $row[1]);
                                    echo $replaceProvider;
                                    ?>
                                </td>
                                <td><?php echo date('d/m/Y ' . T_AT . ' H:i', strtotime($row[3])); ?></td>
                            </tr>
                            <?php
                        }
                        ?>
                    </tbody>
                </table>
            </div>
            <div class="row">
                <div class="col-lg-12 text-center">
                    <ul class="pagination">
                        <?php
                        // Display Previous Page link if applicable.
                        if ($pageNum > 1) {
                            $prevPageLink = "?url=account/reference&pageNum=" . ($pageNum - 1);
                            echo '<li><a href="' . $prevPageLink . '" aria-label="Previous"><span aria-hidden="true">&laquo;</span></a></li>';
                        }

                        for ($i = 1; $i <= $numOfPages; $i++) {
                            $pageLink = "?url=account/reference&pageNum=$i";
                            ?>
                            <li><a href="<?php echo $pageLink; ?>"><?php echo $i; ?></a></li>
                            <?php
                        }

                        // Display Next Page link if applicable.
                        if ($pageNum < $numOfPages) {
                            $nextPageLink = "?url=account/reference&pageNum=" . ($pageNum + 1);
                            echo '<li><a href="' . $nextPageLink . '" aria-label="Next"><span aria-hidden="true">&raquo;</span></a></li>';
                        }

                        sqlsrv_close($conn);
                        ?>
                    </ul>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- extend premium modal -->
<div class="modal fade" id="referral5Modal" tabindex="-1" role="dialog" aria-labelledby="referral5ModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                <h4 class="modal-title" id="referral5ModalLabel"><?php echo TM_H_REFERRAL; ?></h4>
            </div>
            <form method="post" name="j_ref5" action="">
                <div class="modal-body">
                    <div class="col-lg-12 j_alert"></div>
                    <div class="row">
                        <div class="col-lg-12">
                            <div class="alert alert-info">
                                <p><i class="glyphicon glyphicon-exclamation-sign"></i> <?php echo TM_REFERRAL_INFO; ?></p>
                                <p><i class="glyphicon glyphicon-exclamation-sign"></i> <?php echo TM_REFERRAL_INFO3; ?></p>
                                <p class="text-bolder text-success"><?php echo TM_REFERRAL_INFO2; ?></p>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-default" data-dismiss="modal"><?php echo B_CLOSE; ?></button>
                    <input type="submit" class="btn btn-primary" value="<?php echo B_GETREWARD; ?>">
                    <input type="hidden" name="CustomerID" value="<?php echo $getCustomerID; ?>">
                    <img src="assets/images/loading/loader.gif" class="load" alt="<?php echo ALT_LOADING; ?>" title="<?php echo ALT_LOADING; ?>" />
                </div>
            </form>
        </div>
    </div>
</div>

<!-- extend premium modal -->
<div class="modal fade" id="referral10Modal" tabindex="-1" role="dialog" aria-labelledby="referral10ModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                <h4 class="modal-title" id="referral10ModalLabel"><?php echo TM_H_REFERRAL; ?></h4>
            </div>
            <form method="post" name="j_ref10" action="">
                <div class="modal-body">
                    <div class="col-lg-12 j_alert"></div>
                    <div class="row">
                        <div class="col-lg-12">
                            <div class="alert alert-info">
                                <p><i class="glyphicon glyphicon-exclamation-sign"></i> <?php echo TM_REFERRAL_INFO; ?></p>
                                <p><i class="glyphicon glyphicon-exclamation-sign"></i> <?php echo TM_REFERRAL_INFO3; ?></p>
                                <p class="text-bolder text-success"><?php echo TM_REFERRAL_INFO2; ?></p>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-default" data-dismiss="modal"><?php echo B_CLOSE; ?></button>
                    <input type="submit" class="btn btn-primary" value="<?php echo B_GETREWARD; ?>">
                    <input type="hidden" name="CustomerID" value="<?php echo $getCustomerID; ?>">
                    <img src="assets/images/loading/loader.gif" class="load" alt="<?php echo ALT_LOADING; ?>" title="<?php echo ALT_LOADING; ?>" />
                </div>
            </form>
        </div>
    </div>
</div>

<!-- extend premium modal -->
<div class="modal fade" id="referral25Modal" tabindex="-1" role="dialog" aria-labelledby="referral25ModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                <h4 class="modal-title" id="referral25ModalLabel"><?php echo TM_H_REFERRAL; ?></h4>
            </div>
            <form method="post" name="j_ref25" action="">
                <div class="modal-body">
                    <div class="col-lg-12 j_alert"></div>
                    <div class="row">
                        <div class="col-lg-12">
                            <div class="alert alert-info">
                                <p><i class="glyphicon glyphicon-exclamation-sign"></i> <?php echo TM_REFERRAL_INFO; ?></p>
                                <p><i class="glyphicon glyphicon-exclamation-sign"></i> <?php echo TM_REFERRAL_INFO3; ?></p>
                                <p class="text-bolder text-success"><?php echo TM_REFERRAL_INFO2; ?></p>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-default" data-dismiss="modal"><?php echo B_CLOSE; ?></button>
                    <input type="submit" class="btn btn-primary" value="<?php echo B_GETREWARD; ?>">
                    <input type="hidden" name="CustomerID" value="<?php echo $getCustomerID; ?>">
                    <img src="assets/images/loading/loader.gif" class="load" alt="<?php echo ALT_LOADING; ?>" title="<?php echo ALT_LOADING; ?>" />
                </div>
            </form>
        </div>
    </div>
</div>