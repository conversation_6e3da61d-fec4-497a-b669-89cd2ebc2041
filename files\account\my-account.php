<?php $zpanel->checkSession(true); ?>
<header class="page-header">
    <h2>ข้อมูลผู้เล่น</h2>

    <div class="right-wrapper pull-right">
        <ol class="breadcrumbs">
            <li>
                <a href="index.php">
                    <i class="fa fa-home"></i>
                </a>
            </li>
            <li><span>หน้ารวมข้อมูล</span></li>
        </ol>
        <a class="sidebar-right-toggle" data-open="sidebar-right"><i class="fa fa-chevron-left"></i></a>
    </div>
</header>
<div class="row">
    <div class="col-xl-6 col-lg-6">
        <section class="panel panel-transparent">
            <header class="panel-heading">
                <div class="panel-actions">
                    <a href="#" class="panel-action panel-action-toggle" data-panel-toggle></a>
                    <a href="#" class="panel-action panel-action-dismiss" data-panel-dismiss></a>
                </div>

                <h2 class="panel-title"><?php echo T_WEBACC_INFO; ?> </h2>
            </header>
            <div class="panel-body">
                <section class="panel panel-group">
                    <header class="panel-heading bg-primary">

                        <div class="widget-profile-info">
                            <div class="profile-picture">
                                <img src="<?php if (!$userLogin->recUserInfo('url', $conn)) { 
																	echo 'home/images/user.png'; 
																} else { 
																	echo 'http://'.$userLogin->recUserInfo('url', $conn);
																}
														 ?>">
                            </div>
                            <div class="profile-info">
                                <h4 class="name text-weight-semibold">
                                    <?php echo $userLogin->recUserAccount('ID', $conn); ?>#<?php echo $userLogin->recUserAccount('UserNum', $conn); ?>
                                </h4>
                                <h5 class="role">
                                    <?php if($userLogin->recUserAccount('IsDeveloper', $conn) == '0'){ echo "User"; }else{ echo "Administrator"; } ?>
                                </h5>
                                <div class="profile-footer">
                                    <a href="?url=account/edit-account"
                                        class="mb-xs mt-xs mr-xs btn btn-primary"><?php echo B_EDIT_ACCOUNT; ?></a>
                                </div>
                            </div>
                        </div>

                    </header>
                    <div id="accordion">
                        <div class="panel panel-accordion panel-accordion-first">
                            <div class="panel-heading">
                                <h4 class="panel-title">
                                    <a class="accordion-toggle" data-toggle="collapse" data-parent="#accordion"
                                        href="#collapse1One">
                                        <i class="fa fa-check"></i> ข้อมูล
                                    </a>
                                </h4>
                            </div>
                            <div id="collapse1One" class="accordion-body collapse in">
                                <div class="panel-body">
                                    <ul class="simple-bullet-list mb-xlg">
                                        <li class="red">
                                            <span
                                                class="title"><?php echo $userLogin->recUserAccount('IP', $conn); ?></span>
                                            <span class="description truncate">IP ที่ลงทะเบียน.</span>
                                        </li>
                                        <li class="green">
                                            <span
                                                class="title"><?php echo date('d/m/Y H:i', strtotime($userLogin->recUserAccount('createDate', $conn))); ?>
                                            </span>
                                            <span class="description truncate">ลงทะเบียน</span>
                                        </li>
                                        <li class="blue">
                                            <span
                                                class="title"><?php echo date('d/m/Y H:i', strtotime($userLogin->recUserAccount('LoginTime', $conn))); ?></span>
                                            <span class="description truncate">เข้าสู่ระบบ.</span>
                                        </li>
                                        <li class="orange">
                                            <span class="title">
                                                <?php echo $userLogin->recUserAccount('PlayTime', $conn)/60 . ' ' . T_YEARSOLD; ?>
                                                <span class="pull-right"></span></span>
                                            <span class="description truncate">เวลาเล่นรวม.</span>
                                        </li>
                                    </ul>
                                    <hr class="solid mt-sm mb-lg">

                                </div>
                            </div>
                        </div>
                        <div class="panel panel-accordion">
                            <div class="panel-heading">
                                <h4 class="panel-title">
                                    <a class="accordion-toggle" data-toggle="collapse" data-parent="#accordion"
                                        href="#collapse1Two">
                                        <i class="fa fa-comment"></i> Messages
                                    </a>
                                </h4>
                            </div>
                            <div id="collapse1Two" class="accordion-body collapse">
                                <div class="panel-body">
                                    <?php                 
															$CustomerID = $userLogin->recUserAccount('UserNum', $conn);
															$selectUserChars = "SELECT * FROM [".DATABASE_SV."].[dbo].cabal_mail_received_table WHERE ReceiverCharIdx/8 = '$CustomerID'";
															$selectUserCharsParam = array();
															$selectUserCharsOption = array("Scrollable" => SQLSRV_CURSOR_KEYSET);
															$selectUserCharsQuery = sqlsrv_query($conn, $selectUserChars, $selectUserCharsParam, $selectUserCharsOption);
															$selectNumRowsChars = @sqlsrv_num_rows($selectUserCharsQuery);

															if ($selectNumRowsChars) {
																while ($resUserChars = sqlsrv_fetch_array($selectUserCharsQuery, SQLSRV_FETCH_ASSOC)) {
																	$style = $userLogin->decode_style($resUserChars['Style']);
																	$ChangClass = $resUserChars['ChangeClass'];
																
															$name = $userLogin->thaitrans($resUserChars['SenderName']);
															$Title = $userLogin->thaitrans($resUserChars['Title']);
															$Content = $userLogin->thaitrans($resUserChars['Content']);
														?>
                                    <ul class="simple-user-list mb-xlg">
                                        <li>
                                            <figure class="image rounded">
                                                <?php if ($resUserChars['State'] ==1){
																	 echo '<i class="fa fa-envelope-open fa-2x" aria-hidden="true"></i>';
																	}else{
																		echo '<i class="fa fa-envelope fa-2x" aria-hidden="true"></i>';
																	} 
																	?>
                                            </figure>
                                            <span class="title"><?php echo $Title; ?></span>
                                            <span class="message"><?php echo $Content; ?></span>
                                        </li>
                                    </ul>
                                    <?php } ?>
                                    <?php
																}
															
														?>
                                </div>
                            </div>
                        </div>
                    </div>
                </section>

            </div>
        </section>
    </div>
    <div class="col-xl-6 col-lg-6">
        <section class="panel panel-transparent">
            <header class="panel-heading">
                <div class="panel-actions">
                    <a href="#" class="panel-action panel-action-toggle" data-panel-toggle></a>
                    <a href="#" class="panel-action panel-action-dismiss" data-panel-dismiss></a>
                </div>

                <h2 class="panel-title"> <?php echo T_FINANCIALINFO; ?></h2>
            </header>
            <div class="panel-body">
                <section class="panel">
                    <div class="panel-body">
                        <div class="circular-bar circular-bar-xs m-none mt-xs mr-md pull-right">
                            <div class="circular-bar-chart"
                                data-percent="<?php echo $userLogin->recUserGameCash('Cash', $conn)/$userLogin->recUserGameCash('CashTotal', $conn)*100; ?>"
                                data-plugin-options='{ "barColor": "#2baab1", "delay": 300, "size": 50, "lineWidth": 4 }'>
                                <strong>Average</strong>
                                <label><span class="percent">45</span>%</label>
                            </div>
                        </div>


                        <div class="h4 text-weight-bold mb-none">
                            <?php echo number_format($userLogin->recUserGameCash('Cash', $conn)); ?></div>
                        <p class="text-xs text-muted mb-none">Cash</p>
                    </div>
                </section>
                <section class="panel">
                    <div class="panel-body">
                        <div class="circular-bar circular-bar-xs m-none mt-xs mr-md pull-right">
                            <div class="circular-bar-chart"
                                data-percent="<?php echo $userLogin->recUserGameCash('CashBonus', $conn)/$userLogin->recUserGameCash('CashTotal', $conn)*100; ?>"
                                data-plugin-options='{ "barColor": "#2baab1", "delay": 300, "size": 50, "lineWidth": 4 }'>
                                <strong>Average</strong>
                                <label><span class="percent">45</span>%</label>
                            </div>
                        </div>
                        <div class="h4 text-weight-bold mb-none">
                            <?php echo number_format($userLogin->recUserGameCash('CashBonus', $conn)); ?></div>
                        <p class="text-xs text-muted mb-none">Cash Bonus</p>
                    </div>
                </section>
                <section class="panel">
                    <div class="panel-body">
                        <div class="small-chart pull-right" id="sparklineLineDash"></div>
                        <?php 
											if ($zpanel->getConfigByValue('Reward', 'value', $conn) == '1') {  
											echo '<span class="pull-right"><a href="#" data-toggle="modal" data-target="#RewardModal" class="mb-xs mt-xs mr-xs btn btn-default">แลกรีวาด</strong></a></span>';
											} else { 
												echo '';
											} ?>
                        <div class="h4 text-weight-bold mb-none">
                            <?php echo number_format($userLogin->recUserGameCash('Reward', $conn)); ?></div>
                        <p class="text-xs text-muted mb-none">คะแนนสะสม Reward</p>
                    </div>
                </section>
                <section class="panel">
                    <div class="panel-body">
                        <div class="small-chart pull-right" id="sparklineLineDash"></div>
                        <div class="h4 text-weight-bold mb-none">
                            <?php echo number_format($userLogin->recUserTpoint('TPoint', $conn)); ?></div>
                        <p class="text-xs text-muted mb-none">T-Point</p>
                    </div>
                </section>
                <section class="panel">
                    <div class="panel-body">
                        <div class="small-chart pull-right" id="sparklineLineDash"></div>
                        <span class="pull-right"><a href="#" data-toggle="modal" data-target="#WalletModal"
                                class="mb-xs mt-xs mr-xs btn btn-default"><strong><?php echo T_GETMORE; ?></strong></a></span>
                        <div class="h4 text-weight-bold mb-none">
                            <?php echo number_format($userLogin->recUserGameCash('CashTotal', $conn)); ?></div>
                        <p class="text-xs text-muted mb-none">Total Cash</p>
                    </div>
                </section>
            </div>
        </section>
    </div>
    <div class="col-xl-12 col-lg-12">
        <section class="panel panel-transparent">
            <header class="panel-heading panel-heading-transparent">
                <div class="panel-actions">
                    <a href="#" class="panel-action panel-action-toggle" data-panel-toggle></a>
                    <a href="#" class="panel-action panel-action-dismiss" data-panel-dismiss></a>
                </div>

                <h2 class="panel-title">ข้อมูลตัวละคร</h2>
            </header>
            <div class="panel-body">
                <?php                 
                                    $CustomerID = $userLogin->recUserAccount('UserNum', $conn);
                                    $selectUserChars = "SELECT * FROM [".DATABASE_SV."].[dbo].cabal_character_table WHERE CharacterIdx/8 = '$CustomerID' ORDER BY CharacterIdx ASC";
                                    $selectUserCharsParam = array();
                                    $selectUserCharsOption = array("Scrollable" => SQLSRV_CURSOR_KEYSET);
                                    $selectUserCharsQuery = sqlsrv_query($conn, $selectUserChars, $selectUserCharsParam, $selectUserCharsOption);
                                    $selectNumRowsChars = @sqlsrv_num_rows($selectUserCharsQuery);

                                    if ($selectNumRowsChars) {
                                        while ($resUserChars = sqlsrv_fetch_array($selectUserCharsQuery, SQLSRV_FETCH_ASSOC)) {
                                            $style = $userLogin->decode_style($resUserChars['Style']);
                                            $ChangClass = $resUserChars['ChangeClass'];
                                            


									$selectwarpoint = "SELECT * FROM [".DATABASE_SV."].[dbo].cabal_WarExp_Table WHERE CharacterIdx = '$resUserChars[CharacterIdx]'";
									$selectwarpointParam = array();
									$selectwarpointOption = array("Scrollable" => SQLSRV_CURSOR_KEYSET);
									$selectwarpointQuery = sqlsrv_query($conn, $selectwarpoint, $selectwarpointParam, $selectwarpointOption);
									$selectwarpointfetch =  sqlsrv_fetch_array($selectwarpointQuery, SQLSRV_FETCH_ASSOC);

									$selectAp = "SELECT * FROM [".DATABASE_SV."].[dbo].cabal_soul_ability_table WHERE CharacterIdx = '$resUserChars[CharacterIdx]'";
									$selectApParam = array();
									$selectApOption = array("Scrollable" => SQLSRV_CURSOR_KEYSET);
									$selectApQuery = sqlsrv_query($conn, $selectAp, $selectApParam, $selectApOption);
									$selectApfetch =  sqlsrv_fetch_array($selectApQuery, SQLSRV_FETCH_ASSOC);

									$selectDP = "SELECT * FROM [".DATABASE_SV."].[dbo].cabal_DungeonPoint_table WHERE CharacterIdx = '$resUserChars[CharacterIdx]'";
									$selectDPParam = array();
									$selectDPOption = array("Scrollable" => SQLSRV_CURSOR_KEYSET);
									$selectDPQuery = sqlsrv_query($conn, $selectDP, $selectDPParam, $selectDPOption);
									$selectDPfetch =  sqlsrv_fetch_array($selectDPQuery, SQLSRV_FETCH_ASSOC);

									$selectGuild = "SELECT * FROM [".DATABASE_SV."].[dbo].GuildMember WHERE CharacterIndex = '$resUserChars[CharacterIdx]'";
									$selectGuildParam = array();
									$selectGuildOption = array("Scrollable" => SQLSRV_CURSOR_KEYSET);
									$selectGuildQuery = sqlsrv_query($conn, $selectGuild, $selectGuildParam, $selectGuildOption);
									$selectGuildfetch =  sqlsrv_fetch_array($selectGuildQuery, SQLSRV_FETCH_ASSOC);

									$selectGuildn = "SELECT * FROM [".DATABASE_SV."].[dbo].Guild WHERE GuildNo = '$selectGuildfetch[GuildNo]'";
									$selectGuildnParam = array();
									$selectGuildnOption = array("Scrollable" => SQLSRV_CURSOR_KEYSET);
									$selectGuildnQuery = sqlsrv_query($conn, $selectGuildn, $selectGuildnParam, $selectGuildnOption);
									$selectGuildnfetch =  sqlsrv_fetch_array($selectGuildnQuery, SQLSRV_FETCH_ASSOC);
									$name = $userLogin->thaitrans($resUserChars['Name']);
								?>
                <?php if($resUserChars['LEV'] >=1){ ?>
           <div class="col-xl-6 col-lg-12">
                <section class="panel-group mb-xlg">
                    <div class="widget-twitter-profile">
                        <div class="top-image">
                            <img src="assets/images/widget-twitter-xx.jpg" alt="">
                        </div>
                        <div class="profile-info">
                            <div class="profile-picture">
                                <img src="assets/images/cabal/class/<?php echo $style['Class_Name']; ?>.png" alt="">
                            </div>
                            <div class="profile-account">
                                <h3 class="name text-weight-semibold"><?php echo $name; ?></h3>
                                <a href="?url=charecter/charecter&charid=<?php echo $resUserChars['CharacterIdx']; ?>" class="account">#00X0<?php echo $resUserChars['CharacterIdx']; ?></a>
                            </div>
                            <ul class="profile-stats">
                                <li>
                                    <h5 class="stat text-uppercase">LEVEL</h5>
                                    <h4 class="count"><?php echo $resUserChars['LEV']; ?></h4>
                                </li>
                                <li>
                                    <h5 class="stat text-uppercase">DP</h5>
                                    <h4 class="count"><?php echo $selectDPfetch['DP']; ?></h4>
                                </li>
                                <li>
                                    <h5 class="stat text-uppercase">NATION</h5>
                                    <img src="assets/images/cabal/<?php echo  $userLogin->nation($resUserChars['Nation']); ?>.gif"
                                        width="24" height="24" alt="No Nation" class="img-circle">
                                </li>
                                <li>
                                    <div class="btn-group">
                                        <a class="btn btn-primary dropdown-toggle" data-toggle="dropdown"
                                            aria-expanded="true"><i class="fa fa-gear"></i> จัดการ <span
                                                class="caret"></span></a>
                                        <ul class="dropdown-menu pull-right" role="menu">
                                            <li><a
                                                    href="?url=charecter/charecter&charid=<?php echo $resUserChars['CharacterIdx']; ?>"><i
                                                        class="fa fa-angle-right"></i> :ข้อมูลทั้งหมด</a></li>
                                            <li><a
                                                    href="?url=charecter/change-nation&charid=<?php echo $resUserChars['CharacterIdx']; ?>"><i
                                                        class="fa fa-angle-right"></i> :เปลียนประเทศ</a></li>
                                            <li><a
                                                    href="?url=charecter/change-rep&charid=<?php echo $resUserChars['CharacterIdx']; ?>"><i
                                                        class="fa fa-angle-right"></i> :อัพสเตตัส</a></li>
                                            <li><a
                                                    href="?url=charecter/change-charecterhide&charid=<?php echo $resUserChars['CharacterIdx']; ?>"><i
                                                        class="fa fa-angle-right"></i> :ลบตัวละครเวล200</a></li>
                                            <li><a
                                                    href="?url=charecter/change-gamertag&charid=<?php echo $resUserChars['CharacterIdx']; ?>"><i
                                                        class="fa fa-angle-right"></i> :รับชุดผู้เล่นใหม่</a></li>
                                            <li><a
                                                    href="?url=charecter/teleport-char&charid=<?php echo $resUserChars['CharacterIdx']; ?>"><i
                                                        class="fa fa-angle-right"></i> :ติดแมพ</a></li>
                                        </ul>
                                    </div>
                                </li>
                            </ul>
                        </div>
                        <div class="profile-quote">
                            <div class="quote-footer">
                                <span class="datetime">Login: <?php echo date('d/m/Y H:i', strtotime($resUserChars['LoginTime'])); ?> - Logout : <?php echo date('d/m/Y H:i', strtotime($resUserChars['LogoutTime'])); ?></span>
                            </div>
                        </div>
                    </div>
                </section>
                </div>
                </li>
                </ul>

            <?php 
                    }     
                }                
            }
            ?>

            </div>
    </div>


    <!--/ModalLabel/-->
    <div class="modal fade" id="WalletModal" tabindex="-1" role="dialog" aria-labelledby="WalletModalLabel"
        aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span
                            aria-hidden="true">&times;</span></button>
                    <h4 class="modal-title" id="packModalLabel">วิธีเติมเงินด้วย Wallet</h4>
                </div>

                <div class="modal-body">
                    <p class="text-red">เติมเงินได้หลายช่องทาง<span class="text-bolder">เพื่อความสดวก</span></p>

                    <h3 class="text-red">เติมด้วยบัตร True Money</h3>
                    <div class="row">
                        <div class="col-lg-12">
                            <div class="col-lg-12">
                                <ul>
                                    <li>1. เข้าระบบจัดการด้วย Email และ Password ที่ลงทะเบียน</li>
                                    <li>2. นำรหัสบัตร True Money มากรอกที่ <a href="?url=wallet/true"
                                            class="label label-default text-red bg-defaul">เติมบัตร</a></li>
                                    <li>4. รอระบบตรวจสอบบัตร 5 นาที</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                    <h3 class="text-red">เติมด้วย PayPal</h3>
                    <div class="row">
                        <div class="col-lg-12">
                            <div class="col-lg-12">
                                <ul>
                                    <li>กำลังอัพเดดข้อมูล</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-default" data-dismiss="modal"><?php echo B_CLOSE; ?></button>
                </div>
            </div>
        </div>
    </div>


    <!-- reward type modal -->
    <div class="modal fade" id="RewardModal" tabindex="-1" role="dialog" aria-labelledby="RewardModalLabel"
        aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span
                            aria-hidden="true">&times;</span></button>
                    <h4 class="modal-title" id="MyRewardModalLabel">Reward Exchang</h4>
                </div>
                <form method="post" name="j_select_Reward" action="">
                    <div class="modal-body">
                        <div class="col-lg-12 j_alert"></div>
                        <p class="text-info">#Reward ทั้งหมดที่มีอยู่
                            <?php echo number_format($userLogin->recUserGameCash('Reward', $conn)); ?> Reward</p>
                        <p class="text-danger">#ระวังการแลก Reward ช้ำหลายๆครั้ง</p>
                        <p class="text-danger">#ถ้าเกิดแลกผิดกรุณาโดยไม่ได้ตั้งใจกรุณาแจ้ง GM ตรวจสอบให้</p>
                        <div class="form-group">
                            <label for="acctype">Reward Exchang</label>

                        </div>
                    </div>
                    <?php if ($zpanel->getConfigByValue('Reward', 'value', $conn) == '0'){ 
						echo '<div class="alert alert-info fade in nomargin">
										<button aria-hidden="true" data-dismiss="alert" class="close" type="button">×</button>
										<h4>Under Construction!</h4>
										<strong>Under Construction !</strong> หน้าเว็บนี้ ? <a href="" class="alert-link">ปิดปรับปรุงขั่วคราว</a> ขออภัยในความสดวก.
										<p>
                                            <a href="home.php" class="btn btn-info mt-xs mb-xs">Yes, ไปหน้าแรก</a>
											<button class="btn btn-default mt-xs mb-xs" type="button">Not convinced yet</button>
										</p>
									</div>'; 
						}else{; 
					?>
                    <div class="form-group">

                        <div class="col-sm-12">
                            <select id="Rewardtype" name="Rewardtype" data-plugin-selectTwo
                                class="form-control populate js-example-responsive" style="width: 100%;">
                                <option value="">เลือกไอเท็ม</option>'
                                <optgroup label="### อาวุธแฟนขี ###">
                                    <?php                 
																			$userid = $userLogin->recUserAccount('ID', $conn);
																			$selectUserChars = "SELECT * FROM WEB_Reward_Item where itemdetail = 'ไอเท็มอาวุธแฟนชี'";
																			$selectUserCharsParam = array();
																			$selectUserCharsOption = array("Scrollable" => SQLSRV_CURSOR_KEYSET);
																			$selectUserCharsQuery = sqlsrv_query($conn, $selectUserChars, $selectUserCharsParam, $selectUserCharsOption);
																			$selectNumRowsChars = @sqlsrv_num_rows($selectUserCharsQuery);

																			if ($selectNumRowsChars) {
																				while ($resUserChars = sqlsrv_fetch_array($selectUserCharsQuery, SQLSRV_FETCH_ASSOC)) {
																					$item = $resUserChars['itemname'];
																				echo '<option value="'.$resUserChars['id'].'"> '.$resUserChars['itemname'].' '.$resUserChars['reward_price'].' แต้ม</option>';
																				     }
																				 }
																	 ?>
                                </optgroup>
                                <optgroup label="###ไอเท็มชุดแฟนชี###">
                                    <?php                 
																			$userid = $userLogin->recUserAccount('ID', $conn);
																			$selectUserChars = "SELECT * FROM WEB_Reward_Item where itemdetail = 'ไอเท็มชุดแฟนชี'";
																			$selectUserCharsParam = array();
																			$selectUserCharsOption = array("Scrollable" => SQLSRV_CURSOR_KEYSET);
																			$selectUserCharsQuery = sqlsrv_query($conn, $selectUserChars, $selectUserCharsParam, $selectUserCharsOption);
																			$selectNumRowsChars = @sqlsrv_num_rows($selectUserCharsQuery);

																			if ($selectNumRowsChars) {
																				while ($resUserChars = sqlsrv_fetch_array($selectUserCharsQuery, SQLSRV_FETCH_ASSOC)) {
																					$item = $resUserChars['itemname'];
																				echo '<option value="'.$resUserChars['id'].'"> '.$resUserChars['itemname'].' '.$resUserChars['reward_price'].' แต้ม</option>';
																				     }
																				 }
																	 ?>
                                </optgroup>
                                <optgroup label="###ไอเท็มหัวแฟนชี###">
                                    <?php                 
																			$userid = $userLogin->recUserAccount('ID', $conn);
																			$selectUserChars = "SELECT * FROM WEB_Reward_Item where itemdetail = 'ไอเท็มหัวแฟนชี'";
																			$selectUserCharsParam = array();
																			$selectUserCharsOption = array("Scrollable" => SQLSRV_CURSOR_KEYSET);
																			$selectUserCharsQuery = sqlsrv_query($conn, $selectUserChars, $selectUserCharsParam, $selectUserCharsOption);
																			$selectNumRowsChars = @sqlsrv_num_rows($selectUserCharsQuery);

																			if ($selectNumRowsChars) {
																				while ($resUserChars = sqlsrv_fetch_array($selectUserCharsQuery, SQLSRV_FETCH_ASSOC)) {
																					$item = $resUserChars['itemname'];
																				echo '<option value="'.$resUserChars['id'].'"> '.$resUserChars['itemname'].' '.$resUserChars['reward_price'].' แต้ม</option>';
																				     }
																				 }
																	 ?>
                                </optgroup>
                                <optgroup label="### สัตว์เลี้ยง ###">
                                    <?php                 
																			$userid = $userLogin->recUserAccount('ID', $conn);
																			$selectUserChars = "SELECT * FROM WEB_Reward_Item where itemdetail = 'สัตว์เลี้ยง'";
																			$selectUserCharsParam = array();
																			$selectUserCharsOption = array("Scrollable" => SQLSRV_CURSOR_KEYSET);
																			$selectUserCharsQuery = sqlsrv_query($conn, $selectUserChars, $selectUserCharsParam, $selectUserCharsOption);
																			$selectNumRowsChars = @sqlsrv_num_rows($selectUserCharsQuery);

																			if ($selectNumRowsChars) {
																				while ($resUserChars = sqlsrv_fetch_array($selectUserCharsQuery, SQLSRV_FETCH_ASSOC)) {
																					$item = $resUserChars['itemname'];
																				echo '<option value="'.$resUserChars['id'].'"> '.$resUserChars['itemname'].' '.$resUserChars['reward_price'].' แต้ม</option>';
																				     }
																				 }
																	 ?>
                                </optgroup>
                                <optgroup label="### ปีกแฟนชี ###">
                                    <?php                 
																			$userid = $userLogin->recUserAccount('ID', $conn);
																			$selectUserChars = "SELECT * FROM WEB_Reward_Item where itemdetail = 'ปีกแฟนชี'";
																			$selectUserCharsParam = array();
																			$selectUserCharsOption = array("Scrollable" => SQLSRV_CURSOR_KEYSET);
																			$selectUserCharsQuery = sqlsrv_query($conn, $selectUserChars, $selectUserCharsParam, $selectUserCharsOption);
																			$selectNumRowsChars = @sqlsrv_num_rows($selectUserCharsQuery);

																			if ($selectNumRowsChars) {
																				while ($resUserChars = sqlsrv_fetch_array($selectUserCharsQuery, SQLSRV_FETCH_ASSOC)) {
																					$item = $resUserChars['itemname'];
																				echo '<option value="'.$resUserChars['id'].'"> '.$resUserChars['itemname'].' '.$resUserChars['reward_price'].' แต้ม</option>';
																				     }
																				 }
																	 ?>
                                </optgroup>
                                <optgroup label="### ใบคราฟ ###">
                                    <?php                 
																			$userid = $userLogin->recUserAccount('ID', $conn);
																			$selectUserChars = "SELECT * FROM WEB_Reward_Item where itemdetail = 'ใบคราฟ'";
																			$selectUserCharsParam = array();
																			$selectUserCharsOption = array("Scrollable" => SQLSRV_CURSOR_KEYSET);
																			$selectUserCharsQuery = sqlsrv_query($conn, $selectUserChars, $selectUserCharsParam, $selectUserCharsOption);
																			$selectNumRowsChars = @sqlsrv_num_rows($selectUserCharsQuery);

																			if ($selectNumRowsChars) {
																				while ($resUserChars = sqlsrv_fetch_array($selectUserCharsQuery, SQLSRV_FETCH_ASSOC)) {
																					$item = $resUserChars['itemname'];
																				echo '<option value="'.$resUserChars['id'].'"> '.$resUserChars['itemname'].' '.$resUserChars['reward_price'].' แต้ม</option>';
																				     }
																				 }
																	 ?>
                                </optgroup>
                                <optgroup label="### รูน ###">
                                    <?php                 
																			$userid = $userLogin->recUserAccount('ID', $conn);
																			$selectUserChars = "SELECT * FROM WEB_Reward_Item where itemdetail = 'รูน'";
																			$selectUserCharsParam = array();
																			$selectUserCharsOption = array("Scrollable" => SQLSRV_CURSOR_KEYSET);
																			$selectUserCharsQuery = sqlsrv_query($conn, $selectUserChars, $selectUserCharsParam, $selectUserCharsOption);
																			$selectNumRowsChars = @sqlsrv_num_rows($selectUserCharsQuery);

																			if ($selectNumRowsChars) {
																				while ($resUserChars = sqlsrv_fetch_array($selectUserCharsQuery, SQLSRV_FETCH_ASSOC)) {
																					$item = $resUserChars['itemname'];
																				echo '<option value="'.$resUserChars['id'].'"> '.$resUserChars['itemname'].' '.$resUserChars['reward_price'].' แต้ม</option>';
																				     }
																				 }
																	 ?>
                                </optgroup>
                                <optgroup label="### เปลียนรูปมอไชด์ RW PW ###">
                                    <?php                 
																			$userid = $userLogin->recUserAccount('ID', $conn);
																			$selectUserChars = "SELECT * FROM WEB_Reward_Item where itemdetail = 'เปลียนรูปมอไชด์ RW PW'";
																			$selectUserCharsParam = array();
																			$selectUserCharsOption = array("Scrollable" => SQLSRV_CURSOR_KEYSET);
																			$selectUserCharsQuery = sqlsrv_query($conn, $selectUserChars, $selectUserCharsParam, $selectUserCharsOption);
																			$selectNumRowsChars = @sqlsrv_num_rows($selectUserCharsQuery);

																			if ($selectNumRowsChars) {
																				while ($resUserChars = sqlsrv_fetch_array($selectUserCharsQuery, SQLSRV_FETCH_ASSOC)) {
																					$item = $resUserChars['itemname'];
																				echo '<option value="'.$resUserChars['id'].'"> '.$resUserChars['itemname'].' '.$resUserChars['reward_price'].' แต้ม</option>';
																				     }
																				 }
																	 ?>
                                </optgroup>
                            </select>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-default"
                            data-dismiss="modal"><?php echo B_CLOSE; ?></button>
                        <input type="submit" class="btn btn-primary" value="ส่งข้อมูลถึง GM">
                        <input type="hidden" name="userid" value="<?php echo $userid; ?>">
                        <img src="assets/images/loading/loader.gif" class="load" alt="<?php echo ALT_LOADING; ?>"
                            title="<?php echo ALT_LOADING; ?>" />
                    </div>
                </form>
                <?php } ?>
            </div>
        </div>
    </div>