/**
 * ระบบจัดการไอเท็มสำหรับเกม Cabal - Modern ES6+ Implementation
 * <AUTHOR> Panel System
 * @version 2.0
 */

class ItemManager {
    constructor() {
        // Core State
        this.currentUser = null;
        this.items = [];
        this.filteredItems = [];
        this.selectedItem = null;
        this.dataTable = null;
        
        // Form State
        this.formData = {
            userId: '',
            itemCode: '',
            option: 0,
            duration: 31,
            upgrade: '0',
            quantity: 0,
            slot: 0
        };
        
        // UI State
        this.currentStep = 1;
        this.isLoading = false;
        
        // Templates and Settings
        this.templates = this.loadTemplates();
        this.lastUsedValues = this.loadLastUsedValues();
        this.settings = this.loadSettings();
        
        // Binding codes (corrected values)
        this.bindingCodes = {
            id: 4096,        // Bind to Account ID
            char: 524288,    // Bind to Character (corrected)
            equ: 1572864     // Bind on Equip
        };

        // Item Map Name - Slot options for each item type (ported from Python)
        this.itemMapName = {
            "Helm": [
                {"1": "MP"},
                {"2": "DEF"},
                {"3": "DEF RATE"},
                {"4": "CRIT DAMAGE"},
                {"5": "CRIT RATE"},
                {"6": "2SLOT DROP"},
                {"7": "SKILL EXP"},
                {"8": "SWORD SKILL AMP"},
                {"9": "MAGIC SKILL AMP"},
                {"A": "ALL ATTACK UP"},
                {"B": "MAX HP STEAL"},
                {"C": "MAX MP STEAL"},
                {"D": "ALZ DROP AMMOUNT"},
                {"E": "1 SLOT ITEM DROP"},
                {"F": "ALL SKILL AMP"}
            ],
            "Suit": [
                {"1": "HP"},
                {"2": "DEF"},
                {"3": "DEF RATE"},
                {"4": "MP"},
                {"5": "HP AUTO HEAL"},
                {"6": "2 SLOTS ITEM DROP"},
                {"7": "SKILL EXP"},
                {"8": "SWORD SKILL AMP"},
                {"9": "MAGIC SKILL AMP"},
                {"A": "ALL ATTACK UP"},
                {"B": "MP AUTO HEAL"},
                {"C": "MAX CRIT RATE"},
                {"D": "ALZ DROP AMOUNT"},
                {"E": "FLEE RATE"},
                {"F": "ALL SKILL AMP"}
            ],
            "Gloves": [
                {"1": "DEF"},
                {"2": "ATTACK RATE"},
                {"3": "DEF RATE"},
                {"4": "ATTACK"},
                {"5": "HP AUTO HEAL"},
                {"6": "2 SLOTS ITEM DROP"},
                {"7": "SKILL EXP"},
                {"8": "SWORD SKILL AMP"},
                {"9": "MAGIC SKILL AMP"},
                {"A": "ALL ATTACK UP"},
                {"B": "MP AUTO HEAL"},
                {"C": "MAX CRIT RATE"},
                {"D": "HP STEAL"},
                {"E": "MP STEAL"},
                {"F": "ALL SKILL AMP"}
            ],
            "Boots": [
                {"1": "DEF"},
                {"2": "DEF RATE"},
                {"3": "HP"},
                {"4": "MP"},
                {"5": "HP AUTO HEAL"},
                {"6": "2 SLOTS ITEM DROP"},
                {"7": "SKILL EXP"},
                {"8": "SWORD SKILL AMP"},
                {"9": "MAGIC SKILL AMP"},
                {"A": "ALL ATTACK UP"},
                {"B": "MP AUTO HEAL"},
                {"C": "MAX HP STEAL"},
                {"D": "ALZ DROP AMMOUNT"},
                {"E": "FLEE RATE"},
                {"F": "ALL SKILL AMP"}
            ],
            "Weapon": [
                {"1": "ATTACK"},
                {"2": "MAGIC ATTACK"},
                {"3": "ATTACK RATE"},
                {"4": "CRIT DAMAGE"},
                {"5": "CRIT RATE"},
                {"6": "2 SLOTS ITEM DROP"},
                {"7": "SKILL EXP"},
                {"8": "SWORD SKILL AMP"},
                {"9": "MAGIC SKILL AMP"},
                {"A": "ALL ATTACK UP"},
                {"B": "MIN DAMAGE"},
                {"C": "ADD DAMAGE"},
                {"D": "ALZ DROP"},
                {"E": "1 SLOT DROP"},
                {"F": "ALL SKILL AMP"}
            ],
            "Bike": [
                {"1": "HP"},
                {"2": "ATTACK"},
                {"3": "MAGIC ATTACK"},
                {"4": "CRIT DAMAGE"},
                {"5": "CRIT RATE"},
                {"6": "MAX CRIT RATE"},
                {"7": "SWORD SKILL AMP"},
                {"8": "MAGIC SKILL AMP"},
                {"9": "RESIST CRIT RATE"},
                {"A": "RESIST CRIT DAMAGE"},
                {"B": "RESIST SKILL AMP"},
                {"C": "ALL ATTACK UP"},
                {"D": "ALL SKILL AMP"},
                {"E": "NON VALID"},
                {"F": "NON VALID"}
            ]
        };

        // Item type detection patterns
        this.itemTypePatterns = {
            "Helm": /helm|hat|cap|crown|circlet|headband/i,
            "Suit": /suit|armor|chest|plate|mail|robe|vest/i,
            "Gloves": /glove|gauntlet|hand|fist/i,
            "Boots": /boot|shoe|feet|greave/i,
            "Weapon": /sword|blade|staff|wand|bow|gun|axe|mace|dagger|katana|great|force/i,
            "Bike": /bike|mount|vehicle|board/i
        };

        // Initialize
        this.init();
    }

    /**
     * Initialize the system
     */
    async init() {
        try {
            console.log('🚀 Initializing Item Manager...');
            
            // Setup UI
            this.setupProgressSteps();
            this.setupFormValidation();
            this.setupEventListeners();
            this.setupKeyboardShortcuts();
            this.setupAutoSave();
            
            // Load data
            await this.loadItemsData();
            await this.initializeDataTable();
            
            // Restore last session
            this.restoreLastSession();
            
            console.log('✅ Item Manager initialized successfully');
            this.showNotification('ระบบพร้อมใช้งาน', 'success');
            
        } catch (error) {
            console.error('❌ Error initializing Item Manager:', error);
            this.showNotification('เกิดข้อผิดพลาดในการเริ่มต้นระบบ', 'error');
        }
    }

    /**
     * Setup progress steps UI
     */
    setupProgressSteps() {
        const steps = document.querySelectorAll('.progress-step');
        steps.forEach((step, index) => {
            step.addEventListener('click', () => this.goToStep(index + 1));
        });
    }

    /**
     * Navigate to specific step
     */
    goToStep(stepNumber) {
        if (stepNumber < 1 || stepNumber > 4) return;
        
        // Update current step
        this.currentStep = stepNumber;
        
        // Update UI
        document.querySelectorAll('.progress-step').forEach((step, index) => {
            step.classList.toggle('active', index + 1 <= stepNumber);
            step.classList.toggle('completed', index + 1 < stepNumber);
        });
        
        // Show/hide relevant sections
        this.updateStepVisibility();
        
        // Update step indicator
        this.updateStepIndicator();
    }

    /**
     * Update step visibility
     */
    updateStepVisibility() {
        const sections = {
            1: '.user-check-section',
            2: '.item-selection-section', 
            3: '.item-form-section',
            4: '.confirmation-section'
        };
        
        Object.entries(sections).forEach(([step, selector]) => {
            const element = document.querySelector(selector);
            if (element) {
                element.style.display = parseInt(step) <= this.currentStep ? 'block' : 'none';
            }
        });
    }

    /**
     * Setup form validation
     */
    setupFormValidation() {
        const form = document.querySelector('form[name="j_add_adminitems"]');
        if (!form) return;
        
        form.addEventListener('submit', (e) => {
            if (!this.validateForm()) {
                e.preventDefault();
                return false;
            }
            
            this.saveLastUsedValues();
            this.showNotification('กำลังส่งไอเท็ม...', 'info');
        });
        
        // Real-time validation
        const inputs = form.querySelectorAll('input, select');
        inputs.forEach(input => {
            input.addEventListener('blur', () => this.validateField(input));
            input.addEventListener('input', () => this.handleInputChange(input));
        });
    }

    /**
     * Validate entire form
     */
    validateForm() {
        let isValid = true;
        const errors = [];
        
        // Check UserID
        if (!this.currentUser) {
            errors.push('กรุณาตรวจสอบ UserID ก่อน');
            isValid = false;
        }
        
        // Check Item Code
        const itemCode = document.getElementById('input_Item')?.value;
        if (!itemCode || itemCode.trim() === '') {
            errors.push('กรุณาเลือกรหัสไอเท็ม');
            isValid = false;
        }
        
        // Check numeric values
        const option = document.getElementById('input_Option')?.value;
        if (option && (isNaN(option) || option < 0 || option > 255)) {
            errors.push('ค่าออฟชั่นต้องอยู่ระหว่าง 0-255');
            isValid = false;
        }
        
        if (!isValid) {
            this.showNotification(errors.join('<br>'), 'error');
        }
        
        return isValid;
    }

    /**
     * Validate individual field
     */
    validateField(input) {
        const value = input.value.trim();
        let isValid = true;
        let message = '';
        
        switch (input.id) {
            case 'input_checkid':
                if (!value) {
                    isValid = false;
                    message = 'กรุณากรอก UserID';
                }
                break;
                
            case 'input_Item':
                if (!value) {
                    isValid = false;
                    message = 'กรุณาเลือกรหัสไอเท็ม';
                }
                break;
                
            case 'input_Option':
                if (value && (isNaN(value) || value < 0 || value > 255)) {
                    isValid = false;
                    message = 'ค่าออฟชั่นต้องอยู่ระหว่าง 0-255';
                }
                break;
        }
        
        // Update UI
        input.classList.toggle('is-invalid', !isValid);
        input.classList.toggle('is-valid', isValid && value);
        
        // Show/hide error message
        const feedback = input.parentNode.querySelector('.invalid-feedback');
        if (feedback) {
            feedback.textContent = message;
        }
        
        return isValid;
    }

    /**
     * Setup event listeners
     */
    setupEventListeners() {
        // User ID check form
        const checkForm = document.querySelector('form[name="j_checkid"]');
        if (checkForm) {
            checkForm.addEventListener('submit', (e) => this.handleUserCheck(e));
        }

        // Item selection
        const itemInput = document.getElementById('input_Item');
        if (itemInput) {
            itemInput.addEventListener('input', (e) => this.handleItemSelection(e));
            itemInput.addEventListener('change', (e) => this.handleItemSelection(e));
        }

        // Form inputs
        document.querySelectorAll('.form-control').forEach(input => {
            input.addEventListener('input', (e) => this.handleInputChange(e.target));
        });

        // Quick action buttons
        this.setupQuickActions();

        // Table interactions
        this.setupTableEvents();

        // Converter tools
        this.setupConverterTools();
    }

    /**
     * Setup quick action buttons
     */
    setupQuickActions() {
        // Help button
        const helpBtn = document.getElementById('helpBtn');
        if (helpBtn) {
            helpBtn.addEventListener('click', () => this.showHelp());
        }

        // Reset button
        const resetBtn = document.getElementById('resetBtn');
        if (resetBtn) {
            resetBtn.addEventListener('click', () => this.resetForm());
        }

        // Template button
        const templateBtn = document.getElementById('templateBtn');
        if (templateBtn) {
            templateBtn.addEventListener('click', () => this.showTemplateDialog());
        }

        // Advanced Editor button
        const advancedEditorBtn = document.getElementById('advancedEditorBtn');
        if (advancedEditorBtn) {
            console.log('✅ Advanced Editor button found, adding event listener');
            advancedEditorBtn.addEventListener('click', () => {
                console.log('🔧 Advanced Editor button clicked');
                this.showAdvancedEditor();
            });
        } else {
            console.warn('⚠️ Advanced Editor button not found');
        }

        // Search toggle
        const searchToggleBtn = document.getElementById('searchToggleBtn');
        if (searchToggleBtn) {
            searchToggleBtn.addEventListener('click', () => this.toggleSearch());
        }

        // Refresh table
        const refreshBtn = document.getElementById('refreshTableBtn');
        if (refreshBtn) {
            refreshBtn.addEventListener('click', () => this.refreshItemsTable());
        }
    }

    /**
     * Setup keyboard shortcuts
     */
    setupKeyboardShortcuts() {
        document.addEventListener('keydown', (e) => {
            // Ctrl + S = Save/Submit
            if (e.ctrlKey && e.key === 's') {
                e.preventDefault();
                const submitBtn = document.querySelector('button[name="btn_savechange"]');
                if (submitBtn && !submitBtn.disabled) {
                    submitBtn.click();
                }
            }
            
            // Ctrl + R = Reset
            if (e.ctrlKey && e.key === 'r') {
                e.preventDefault();
                this.resetForm();
            }
            
            // F1 = Help
            if (e.key === 'F1') {
                e.preventDefault();
                this.showHelp();
            }
            
            // Escape = Close dialogs
            if (e.key === 'Escape') {
                this.closeAllDialogs();
            }
        });
    }

    /**
     * Setup auto-save functionality
     */
    setupAutoSave() {
        setInterval(() => {
            this.autoSave();
        }, 30000); // Auto-save every 30 seconds
    }

    /**
     * Handle user ID check
     */
    async handleUserCheck(event) {
        event.preventDefault();

        const formData = new FormData(event.target);
        const userId = formData.get('input_checkid');

        if (!userId || userId.trim() === '') {
            this.showNotification('กรุณากรอก UserID', 'warning');
            return;
        }

        this.setLoading(true);

        try {
            // The PHP will handle the actual check
            // We just need to update the UI state
            this.currentUser = { id: userId };
            this.goToStep(2);

        } catch (error) {
            console.error('Error checking user:', error);
            this.showNotification('เกิดข้อผิดพลาดในการตรวจสอบ UserID', 'error');
        } finally {
            this.setLoading(false);
        }
    }

    /**
     * Handle item selection
     */
    handleItemSelection(event) {
        const itemCode = event.target.value;

        if (!itemCode) {
            this.selectedItem = null;
            return;
        }

        // Find item in loaded data
        const item = this.items.find(item => item.id === itemCode);
        if (item) {
            this.selectedItem = item;
            this.updateItemPreview(item);
            this.goToStep(3);
        }

        // Update binding codes
        this.updateBindingCodes();
    }

    /**
     * Handle input changes
     */
    handleInputChange(input) {
        const { id, value } = input;

        // Update form data
        switch (id) {
            case 'input_Item':
                this.formData.itemCode = value;
                break;
            case 'input_Option':
                this.formData.option = parseInt(value) || 0;
                break;
            case 'input_Dur':
                this.formData.duration = parseInt(value) || 31;
                break;
            case 'input_Upgrade':
                this.formData.upgrade = value;
                break;
            case 'input_quantity':
                this.formData.quantity = parseInt(value) || 0;
                break;
            case 'input_Slot':
                this.formData.slot = parseInt(value) || 0;
                break;
        }

        // Update calculations
        this.updateBindingCodes();
        this.updateConverterTools();

        // Validate field
        this.validateField(input);

        // Auto-save
        this.saveLastUsedValues();
    }

    /**
     * Update binding codes
     */
    updateBindingCodes() {
        const itemCode = parseFloat(document.getElementById('input_Item')?.value) || 0;
        const bindId = parseFloat(document.getElementById('bind_id')?.value) || this.bindingCodes.id;
        const bindChar = parseFloat(document.getElementById('bind_char')?.value) || this.bindingCodes.char;
        const bindEqu = parseFloat(document.getElementById('bind_equ')?.value) || this.bindingCodes.equ;

        // Calculate binding codes
        const codeBid = itemCode + bindId;
        const codeBchar = itemCode + bindChar;
        const codeBequ = itemCode + bindEqu;

        // Update output fields
        const bidOutput = document.getElementById('codeoutput_bid');
        const bcharOutput = document.getElementById('codeoutput_bchar');
        const bequOutput = document.getElementById('codeoutput_bequ');

        if (bidOutput) bidOutput.value = codeBid.toFixed(0);
        if (bcharOutput) bcharOutput.value = codeBchar.toFixed(0);
        if (bequOutput) bequOutput.value = codeBequ.toFixed(0);
    }

    /**
     * Setup converter tools
     */
    setupConverterTools() {
        const hexInput = document.getElementById('hextodec');
        const decInput = document.getElementById('dectohex');
        const decOutput = document.getElementById('deccode');
        const hexOutput = document.getElementById('hexcode');

        if (hexInput) {
            hexInput.addEventListener('input', (e) => {
                const hexValue = e.target.value;
                if (hexValue && decOutput) {
                    const decValue = parseInt(hexValue, 16);
                    decOutput.value = isNaN(decValue) ? '' : decValue.toString();
                }
            });
        }

        if (decInput) {
            decInput.addEventListener('input', (e) => {
                const decValue = e.target.value;
                if (decValue && hexOutput) {
                    const hexValue = parseInt(decValue).toString(16).toUpperCase();
                    hexOutput.value = isNaN(parseInt(decValue)) ? '' : hexValue;
                }
            });
        }
    }

    /**
     * Update converter tools
     */
    updateConverterTools() {
        const hexInput = document.getElementById('hextodec');
        const decInput = document.getElementById('dectohex');
        const decOutput = document.getElementById('deccode');
        const hexOutput = document.getElementById('hexcode');

        if (hexInput && decOutput) {
            const hexValue = hexInput.value;
            if (hexValue) {
                const decValue = parseInt(hexValue, 16);
                decOutput.value = isNaN(decValue) ? '' : decValue.toString();
            }
        }

        if (decInput && hexOutput) {
            const decValue = decInput.value;
            if (decValue) {
                const hexValue = parseInt(decValue).toString(16).toUpperCase();
                hexOutput.value = isNaN(parseInt(decValue)) ? '' : hexValue;
            }
        }
    }

    /**
     * Load items data from XML file
     */
    async loadItemsData() {
        try {
            const response = await fetch('files/game_systems/import/item_fixed.dec');
            const xmlText = await response.text();
            const parser = new DOMParser();
            const xmlDoc = parser.parseFromString(xmlText, 'text/xml');

            const items = xmlDoc.querySelectorAll('item');
            this.items = Array.from(items).map(item => ({
                id: item.getAttribute('id'),
                name: item.getAttribute('name')
            }));

            console.log(`Loaded ${this.items.length} items`);

            // Update datalist
            this.updateDatalist();

        } catch (error) {
            console.error('Error loading items data:', error);
            this.showNotification('เกิดข้อผิดพลาดในการโหลดข้อมูลไอเท็ม', 'error');
        }
    }

    /**
     * Update datalist with items
     */
    updateDatalist() {
        const datalist = document.getElementById('items_list');
        if (!datalist) return;

        datalist.innerHTML = '';

        this.items.forEach(item => {
            const option = document.createElement('option');
            option.value = item.id;
            option.textContent = `${item.id} - ${item.name}`;
            datalist.appendChild(option);
        });
    }

    /**
     * Initialize data table
     */
    async initializeDataTable() {
        const tableBody = document.getElementById('itemsTableBody');
        if (!tableBody) return;

        tableBody.innerHTML = '';

        this.items.forEach(item => {
            const row = document.createElement('tr');
            row.innerHTML = `
                <td>${item.id}</td>
                <td>${item.name}</td>
            `;
            row.style.cursor = 'pointer';
            row.addEventListener('click', () => this.selectItemFromTable(item.id, item.name));
            tableBody.appendChild(row);
        });
    }

    /**
     * Select item from table
     */
    selectItemFromTable(id, name) {
        const inputItem = document.getElementById('input_Item');
        if (inputItem) {
            inputItem.value = id;
            inputItem.dispatchEvent(new Event('input', { bubbles: true }));
            console.log(`Selected item: ${id} - ${name}`);
        }
    }

    /**
     * Setup table events
     */
    setupTableEvents() {
        // Search functionality
        const searchInput = document.getElementById('itemSearchInput');
        if (searchInput) {
            searchInput.addEventListener('input', (e) => {
                this.filterTable(e.target.value);
            });
        }
    }

    /**
     * Filter table based on search query
     */
    filterTable(query) {
        const tableBody = document.getElementById('itemsTableBody');
        if (!tableBody) return;

        const rows = tableBody.querySelectorAll('tr');

        rows.forEach(row => {
            const text = row.textContent.toLowerCase();
            const matches = text.includes(query.toLowerCase());
            row.style.display = matches ? '' : 'none';
        });
    }

    /**
     * Toggle search box
     */
    toggleSearch() {
        const searchBox = document.getElementById('searchBox');
        if (searchBox) {
            const isVisible = searchBox.style.display !== 'none';
            searchBox.style.display = isVisible ? 'none' : 'block';

            if (!isVisible) {
                const searchInput = document.getElementById('itemSearchInput');
                if (searchInput) {
                    searchInput.focus();
                }
            }
        }
    }

    /**
     * Refresh items table
     */
    async refreshItemsTable() {
        this.setLoading(true);
        try {
            await this.loadItemsData();
            await this.initializeDataTable();
            this.showNotification('รีเฟรชตารางเรียบร้อย', 'success');
        } catch (error) {
            console.error('Error refreshing table:', error);
            this.showNotification('เกิดข้อผิดพลาดในการรีเฟรชตาราง', 'error');
        } finally {
            this.setLoading(false);
        }
    }

    /**
     * Show notification
     */
    showNotification(message, type = 'info') {
        if (typeof Swal !== 'undefined') {
            const config = {
                title: type === 'error' ? 'ข้อผิดพลาด' :
                       type === 'success' ? 'สำเร็จ' :
                       type === 'warning' ? 'คำเตือน' : 'แจ้งเตือน',
                html: message,
                icon: type,
                timer: type === 'success' ? 2000 : undefined,
                showConfirmButton: type !== 'success'
            };

            Swal.fire(config);
        } else {
            alert(message);
        }
    }

    /**
     * Set loading state
     */
    setLoading(isLoading) {
        this.isLoading = isLoading;

        // Update UI elements
        const submitBtn = document.querySelector('button[name="btn_savechange"]');
        if (submitBtn) {
            submitBtn.disabled = isLoading;
            submitBtn.innerHTML = isLoading ?
                '<i class="fas fa-spinner fa-spin mr-2"></i>กำลังประมวลผล...' :
                '<i class="fas fa-gift mr-2"></i>ยืนยันและส่งไอเท็ม';
        }

        // Show/hide loading overlay if exists
        const loadingOverlay = document.getElementById('loadingOverlay');
        if (loadingOverlay) {
            loadingOverlay.style.display = isLoading ? 'flex' : 'none';
        }
    }

    /**
     * Load templates from localStorage
     */
    loadTemplates() {
        try {
            const saved = localStorage.getItem('itemManager_templates');
            return saved ? JSON.parse(saved) : {};
        } catch (error) {
            console.error('Error loading templates:', error);
            return {};
        }
    }

    /**
     * Load last used values from localStorage
     */
    loadLastUsedValues() {
        try {
            const saved = localStorage.getItem('itemManager_lastUsed');
            return saved ? JSON.parse(saved) : {};
        } catch (error) {
            console.error('Error loading last used values:', error);
            return {};
        }
    }

    /**
     * Load settings from localStorage
     */
    loadSettings() {
        try {
            const saved = localStorage.getItem('itemManager_settings');
            return saved ? JSON.parse(saved) : {
                autoSave: true,
                showHelp: true,
                theme: 'default'
            };
        } catch (error) {
            console.error('Error loading settings:', error);
            return {
                autoSave: true,
                showHelp: true,
                theme: 'default'
            };
        }
    }

    /**
     * Save last used values
     */
    saveLastUsedValues() {
        try {
            const values = {
                itemCode: document.getElementById('input_Item')?.value || '',
                option: document.getElementById('input_Option')?.value || '',
                duration: document.getElementById('input_Dur')?.value || '',
                upgrade: document.getElementById('input_Upgrade')?.value || '',
                quantity: document.getElementById('input_quantity')?.value || '',
                slot: document.getElementById('input_Slot')?.value || '',
                timestamp: Date.now()
            };

            localStorage.setItem('itemManager_lastUsed', JSON.stringify(values));
        } catch (error) {
            console.error('Error saving last used values:', error);
        }
    }

    /**
     * Restore last session
     */
    restoreLastSession() {
        if (!this.settings.autoSave) return;

        try {
            const lastUsed = this.lastUsedValues;
            if (!lastUsed.timestamp) return;

            // Only restore if less than 24 hours old
            const hoursSinceLastUse = (Date.now() - lastUsed.timestamp) / (1000 * 60 * 60);
            if (hoursSinceLastUse > 24) return;

            // Restore values
            Object.entries(lastUsed).forEach(([key, value]) => {
                if (key === 'timestamp') return;

                const elementId = key === 'itemCode' ? 'input_Item' :
                                 key === 'option' ? 'input_Option' :
                                 key === 'duration' ? 'input_Dur' :
                                 key === 'upgrade' ? 'input_Upgrade' :
                                 key === 'quantity' ? 'input_quantity' :
                                 key === 'slot' ? 'input_Slot' : null;

                const element = document.getElementById(elementId);
                if (element && value) {
                    element.value = value;
                }
            });

            console.log('Session restored from last use');

        } catch (error) {
            console.error('Error restoring session:', error);
        }
    }

    /**
     * Auto-save functionality
     */
    autoSave() {
        if (!this.settings.autoSave) return;
        this.saveLastUsedValues();
    }

    /**
     * Reset form
     */
    resetForm() {
        const form = document.querySelector('form[name="j_add_adminitems"]');
        if (form) {
            form.reset();

            // Clear validation classes
            form.querySelectorAll('.is-valid, .is-invalid').forEach(element => {
                element.classList.remove('is-valid', 'is-invalid');
            });

            // Reset state
            this.selectedItem = null;
            this.currentStep = 1;
            this.goToStep(1);

            this.showNotification('รีเซ็ตฟอร์มเรียบร้อย', 'success');
        }
    }

    /**
     * Show help dialog
     */
    showHelp() {
        const helpContent = `
            <div class="text-left">
                <h5><i class="fas fa-keyboard mr-2"></i>คีย์บอร์ดลัด</h5>
                <ul>
                    <li><kbd>Ctrl + S</kbd> - ส่งไอเท็ม</li>
                    <li><kbd>Ctrl + R</kbd> - รีเซ็ตฟอร์ม</li>
                    <li><kbd>F1</kbd> - แสดงความช่วยเหลือ</li>
                    <li><kbd>Esc</kbd> - ปิดหน้าต่าง</li>
                </ul>

                <h5><i class="fas fa-info-circle mr-2"></i>วิธีใช้งาน</h5>
                <ol>
                    <li>กรอก UserID ของผู้เล่น</li>
                    <li>เลือกรหัสไอเท็มที่ต้องการ</li>
                    <li>กำหนดค่าต่างๆ ของไอเท็ม</li>
                    <li>กดยืนยันเพื่อส่งไอเท็ม</li>
                </ol>

                <h5><i class="fas fa-lightbulb mr-2"></i>เทคนิค</h5>
                <ul>
                    <li>ใช้ตารางด้านขางเพื่อค้นหาไอเท็มได้เร็วขึ้น</li>
                    <li>ระบบจะบันทึกค่าล่าสุดอัตโนมัติ</li>
                    <li>ใช้เครื่องมือแปลง HEX/DEC ได้ตลอดเวลา</li>
                </ul>
            </div>
        `;

        this.showNotification(helpContent, 'info');
    }

    /**
     * Show template dialog
     */
    showTemplateDialog() {
        // Implementation for template management
        this.showNotification('ฟีเจอร์ Template กำลังพัฒนา', 'info');
    }

    /**
     * Close all dialogs
     */
    closeAllDialogs() {
        // Close any open modals or dialogs
        if (typeof Swal !== 'undefined') {
            Swal.close();
        }

        // Hide search box
        const searchBox = document.getElementById('searchBox');
        if (searchBox) {
            searchBox.style.display = 'none';
        }
    }

    /**
     * Update step indicator
     */
    updateStepIndicator() {
        // Update any step indicator elements
        const stepIndicator = document.querySelector('.step-indicator');
        if (stepIndicator) {
            stepIndicator.textContent = `ขั้นตอน ${this.currentStep} จาก 4`;
        }
    }

    /**
     * Update item preview
     */
    updateItemPreview(item) {
        // Show item preview if element exists
        const preview = document.getElementById('itemPreview');
        if (preview && item) {
            preview.innerHTML = `
                <div class="item-preview-card">
                    <h6><i class="fas fa-cube mr-2"></i>${item.name}</h6>
                    <p class="text-muted mb-0">รหัส: ${item.id}</p>
                </div>
            `;
            preview.style.display = 'block';
        }
    }

    /**
     * Advanced Item Code Calculator (ported from Python)
     * คำนวณรหัสไอเท็มแบบละเอียด รองรับ Extreme, Divine, Craft
     */
    calculateAdvancedItemCode(itemData) {
        const {
            itemId = 0,
            upgrade = 0,
            extreme = 0,
            divine = 0,
            advanceSetting = 0,
            craft = 'NOT',
            craftHeight = 0,
            craftOption = 'NOT'
        } = itemData;

        // Base calculation
        let itemCode = parseInt(itemId);

        // Add upgrade (multiply by 8192)
        itemCode += upgrade * 8192;

        // Add extreme (multiply by 4294967296)
        itemCode += extreme * 4294967296;

        // Add divine (multiply by 68719476736)
        itemCode += divine * 68719476736;

        // Add advance setting
        itemCode += advanceSetting;

        return itemCode;
    }

    /**
     * Calculate Options Code (ported from Python)
     * คำนวณรหัสออฟชั่นสำหรับ slot ต่างๆ รวมถึง craft option
     */
    calculateOptionsCode(slotsData) {
        const {
            slot1 = 'NOT',
            slot2 = 'NOT',
            slot3 = 'NOT',
            craftOption = 'NOT',
            craftHeight = 0,
            itemType = null,
            itemName = ''
        } = slotsData;

        console.log(`🚀 calculateOptionsCode called with:`, slotsData);

        // Auto-detect item type if name is provided
        let detectedType = itemType;
        if (itemName && !detectedType) {
            detectedType = this.detectItemType(itemName);
            console.log(`🔍 Auto-detected item type: "${detectedType}" from name: "${itemName}"`);
        }

        // Use advanced calculation if item type is detected
        if (detectedType && this.itemMapName[detectedType]) {
            console.log(`✅ Using advanced calculation for item type: "${detectedType}"`);
            const result = this.calculateAdvancedOptionsCode({
                itemName: itemName,
                itemType: detectedType,
                slot1: slot1,
                slot2: slot2,
                slot3: slot3,
                craftOption: craftOption,
                craftHeight: craftHeight
            });
            console.log(`🎯 Advanced calculation result: ${result.optionsCode}`);
            return result.optionsCode;
        }

        // Fallback to simple option map for backward compatibility
        console.log(`⚠️ Falling back to simple option map. detectedType="${detectedType}", itemMapName exists:`, !!this.itemMapName[detectedType]);

        const optionMap = {
            'NOT': 0,
            'STR+1': 1,
            'DEX+1': 2,
            'INT+1': 3,
            'HP+10': 4,
            'MP+10': 5,
            'ATK+1': 6,
            'DEF+1': 7,
            'CRIT+1': 8,
            'RATE+1': 9,
            'DMG+1': 10
        };

        let optionsCode = 0;

        // Slot 1 (bits 0-15)
        if (optionMap[slot1] !== undefined) {
            optionsCode += optionMap[slot1];
            console.log(`📍 Fallback Slot1: ${slot1} → ${optionMap[slot1]}`);
        }

        // Slot 2 (bits 16-31)
        if (optionMap[slot2] !== undefined) {
            optionsCode += optionMap[slot2] * 65536;
            console.log(`📍 Fallback Slot2: ${slot2} → ${optionMap[slot2]} * 65536`);
        }

        // Slot 3 (bits 32-47)
        if (optionMap[slot3] !== undefined) {
            optionsCode += optionMap[slot3] * 4294967296;
            console.log(`📍 Fallback Slot3: ${slot3} → ${optionMap[slot3]} * 4294967296`);
        }

        console.log(`🎯 Fallback calculation result: ${optionsCode}`);
        return optionsCode;
    }

    /**
     * Generate complete item data (ported from Python)
     * สร้างข้อมูลไอเท็มแบบครบถ้วน - คำนวณ Item Code + Binding ก่อน แล้วค่อย Options Code
     */
    generateCompleteItem(formData) {
        try {
            console.log('🚀 generateCompleteItem called with:', formData);

            // Step 1: คำนวณ Base Item Code (Item ID + Upgrade + Extreme + Divine)
            const baseItemCode = this.calculateAdvancedItemCode({
                itemId: formData.itemId,
                upgrade: formData.upgrade,
                extreme: formData.extreme || 0,
                divine: formData.divine || 0,
                advanceSetting: formData.advanceSetting || 0
            });
            console.log(`📦 Base Item Code: ${baseItemCode} (Hex: ${baseItemCode.toString(16).toUpperCase()})`);

            // Step 2: คำนวณ Binding Code และรวมเข้ากับ Item Code
            let bindingCode = 0;
            let finalItemCode = baseItemCode;

            if (formData.binding && formData.binding !== 'none') {
                bindingCode = this.calculateBindingCode(formData.binding);
                finalItemCode = baseItemCode + bindingCode;
                console.log(`🔗 Binding Code: ${bindingCode} (${formData.binding.toUpperCase()})`);
                console.log(`🎯 Final Item Code: ${finalItemCode} (Base: ${baseItemCode} + Binding: ${bindingCode})`);
            } else {
                console.log('🔗 No binding applied');
            }

            // Step 3: คำนวณ Options Code แยกต่างหาก
            const optionsResult = this.calculateAdvancedOptionsCode({
                slot1: formData.slot1,
                slot2: formData.slot2,
                slot3: formData.slot3,
                craftOption: formData.craftOption || 'NOT',
                craftHeight: formData.craftHeight || 0,
                itemName: formData.itemName || '',
                itemType: formData.itemType || null
            });
            const optionsCode = optionsResult.optionsCode || 0;
            console.log(`⚙️ Options Code: ${optionsCode} (Hex: ${optionsCode.toString(16).toUpperCase()})`);
            console.log(`🔍 Options Result:`, optionsResult);

            // Step 4: สร้างผลลัพธ์สุดท้าย
            const result = {
                success: true,

                // Item Code Information
                baseItemCode: baseItemCode,
                bindingCode: bindingCode,
                itemCode: finalItemCode,

                // Options Code Information
                optionsCode: optionsCode,

                // Hex Values
                hexBaseItemCode: baseItemCode.toString(16).toUpperCase(),
                hexBindingCode: bindingCode > 0 ? bindingCode.toString(16).toUpperCase() : '0',
                hexItemCode: finalItemCode.toString(16).toUpperCase(),
                hexOptionsCode: optionsCode.toString(16).toUpperCase(),

                // Additional Information
                binding: formData.binding || 'none',
                duration: formData.duration || 31,
                itemType: formData.itemType || 'Unknown',

                // Slot Details (from options calculation)
                slotDetails: optionsResult.slotDetails || null,

                // Calculation Steps (for debugging)
                calculationSteps: {
                    step1: `Base Item Code: ${baseItemCode}`,
                    step2: `Binding Code: +${bindingCode} (${formData.binding || 'none'})`,
                    step3: `Final Item Code: ${finalItemCode}`,
                    step4: `Options Code: ${optionsCode} (separate)`
                }
            };

            console.log('✅ Complete item generation successful:', result);
            return result;

        } catch (error) {
            console.error('❌ Error generating item:', error);
            return {
                success: false,
                error: error.message
            };
        }
    }

    /**
     * Handle advanced calculation
     */
    handleAdvancedCalculation(form) {
        const formData = new FormData(form);
        const data = Object.fromEntries(formData.entries());

        // Convert to numbers
        data.itemId = parseInt(data.itemId);
        data.upgrade = parseInt(data.upgrade);
        data.extreme = parseInt(data.extreme || 0);
        data.divine = parseInt(data.divine || 0);
        data.duration = parseInt(data.duration || 31);

        const result = this.generateCompleteItem(data);

        const resultDiv = document.getElementById('advancedResult');
        if (result.success) {
            resultDiv.innerHTML = `
                <div class="alert alert-success">
                    <h6><i class="fas fa-check-circle mr-2"></i>คำนวณสำเร็จ</h6>

                    <!-- Calculation Order Info -->
                    <div class="alert alert-info mb-3">
                        <h6><i class="fas fa-info-circle mr-2"></i>ลำดับการคำนวณ</h6>
                        <small>
                            1. คำนวณ Item Code + Binding ก่อน<br>
                            2. คำนวณ Options Code แยกต่างหาก
                        </small>
                    </div>

                    <!-- Calculation Steps -->
                    <div class="card mb-3">
                        <div class="card-header bg-light">
                            <h6 class="mb-0">ขั้นตอนการคำนวณ</h6>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-6">
                                    <h6 class="text-primary">Step 1-2: Item Code + Binding</h6>
                                    <p class="mb-1"><strong>Base Item Code:</strong> ${result.baseItemCode}</p>
                                    <p class="mb-1"><strong>Binding Code:</strong> +${result.bindingCode} (${result.binding.toUpperCase()})</p>
                                    <p class="mb-3"><strong>Final Item Code:</strong> <span class="badge badge-primary">${result.itemCode}</span></p>
                                </div>
                                <div class="col-md-6">
                                    <h6 class="text-warning">Step 3: Options Code (แยก)</h6>
                                    <p class="mb-1"><strong>Item Type:</strong> ${result.itemType}</p>
                                    <p class="mb-1"><strong>Options Code:</strong> <span class="badge badge-warning">${result.optionsCode}</span></p>
                                    <p class="mb-3"><strong>Duration:</strong> ${result.duration} days</p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Final Results -->
                    <div class="row">
                        <div class="col-md-6">
                            <h6 class="text-primary">Final Decimal Values:</h6>
                            <p><strong>Item Code:</strong> <span class="badge badge-primary">${result.itemCode}</span></p>
                            <p><strong>Options Code:</strong> <span class="badge badge-warning">${result.optionsCode}</span></p>
                        </div>
                        <div class="col-md-6">
                            <h6 class="text-success">Final Hexadecimal Values:</h6>
                            <p><strong>Item Code:</strong> <span class="badge badge-success">${result.hexItemCode}</span></p>
                            <p><strong>Options Code:</strong> <span class="badge badge-info">${result.hexOptionsCode}</span></p>
                        </div>
                    </div>

                    <hr>
                    <div class="alert alert-success">
                        <small><strong>สำคัญ:</strong> ใช้ Item Code และ Options Code แยกกันในระบบเกม</small>
                    </div>

                    <button class="btn btn-sm btn-info" onclick="window.itemManager.copyToMainForm(${result.itemCode}, ${result.optionsCode})">
                        <i class="fas fa-copy mr-1"></i>Copy to Main Form
                    </button>
                </div>
            `;
        } else {
            resultDiv.innerHTML = `
                <div class="alert alert-danger">
                    <h6><i class="fas fa-exclamation-triangle mr-2"></i>เกิดข้อผิดพลาด</h6>
                    <p>${result.error}</p>
                </div>
            `;
        }

        resultDiv.style.display = 'block';
    }

    /**
     * Copy calculated values to main form
     */
    copyToMainForm(itemCode, optionsCode) {
        const itemInput = document.getElementById('input_Item');
        const optionInput = document.getElementById('input_Option');

        if (itemInput) {
            itemInput.value = itemCode;
            itemInput.dispatchEvent(new Event('input', { bubbles: true }));
        }

        if (optionInput) {
            optionInput.value = optionsCode;
            optionInput.dispatchEvent(new Event('input', { bubbles: true }));
        }

        this.showNotification('คัดลอกค่าไปยังฟอร์มหลักแล้ว', 'success');

        // Close the advanced editor
        if (typeof Swal !== 'undefined') {
            Swal.close();
        }
    }

    /**
     * Convert HEX to Decimal
     */
    convertHexToDec(hexValue) {
        try {
            const cleanHex = hexValue.replace(/^0x/i, '');
            const decimal = parseInt(cleanHex, 16);
            return isNaN(decimal) ? 0 : decimal;
        } catch (error) {
            console.error('Error converting HEX to DEC:', error);
            return 0;
        }
    }

    /**
     * Convert Decimal to HEX
     */
    convertDecToHex(decValue) {
        try {
            const number = parseInt(decValue);
            return isNaN(number) ? '0' : number.toString(16).toUpperCase();
        } catch (error) {
            console.error('Error converting DEC to HEX:', error);
            return '0';
        }
    }

    /**
     * Calculate binding code
     */
    calculateBindingCode(bindingType) {
        const bindingCodes = {
            id: 4096,
            char: 524288,
            equ: 1572864
        };

        return bindingCodes[bindingType] || 0;
    }

    /**
     * Validate form data
     */
    validateFormData(formData) {
        try {
            // Check required fields
            const requiredFields = ['userId', 'itemCode'];
            for (const field of requiredFields) {
                if (!formData[field] || formData[field].trim() === '') {
                    console.error(`Required field missing: ${field}`);
                    return false;
                }
            }

            // Validate numeric fields
            const numericFields = ['itemCode', 'option', 'duration', 'quantity', 'slot'];
            for (const field of numericFields) {
                if (formData[field] && isNaN(parseInt(formData[field]))) {
                    console.error(`Invalid numeric value for field: ${field}`);
                    return false;
                }
            }

            // Validate ranges
            const upgrade = parseInt(formData.upgrade || 0);
            if (upgrade < 0 || upgrade > 20) {
                console.error('Upgrade must be between 0-20');
                return false;
            }

            const duration = parseInt(formData.duration || 31);
            if (duration < 1 || duration > 999) {
                console.error('Duration must be between 1-999');
                return false;
            }

            return true;
        } catch (error) {
            console.error('Error validating form data:', error);
            return false;
        }
    }

    /**
     * Show template dialog (placeholder)
     */
    showTemplateDialog() {
        if (typeof Swal !== 'undefined') {
            Swal.fire({
                title: 'Template System',
                html: `
                    <div class="text-left">
                        <h6>Available Templates:</h6>
                        <div class="list-group">
                            <button class="list-group-item list-group-item-action" onclick="window.itemManager.loadTemplate('weapon')">
                                <i class="fas fa-sword mr-2"></i>Weapon Template
                            </button>
                            <button class="list-group-item list-group-item-action" onclick="window.itemManager.loadTemplate('armor')">
                                <i class="fas fa-shield-alt mr-2"></i>Armor Template
                            </button>
                            <button class="list-group-item list-group-item-action" onclick="window.itemManager.loadTemplate('accessory')">
                                <i class="fas fa-ring mr-2"></i>Accessory Template
                            </button>
                        </div>
                        <hr>
                        <button class="btn btn-success btn-sm" onclick="window.itemManager.saveCurrentAsTemplate()">
                            <i class="fas fa-save mr-1"></i>Save Current as Template
                        </button>
                    </div>
                `,
                showConfirmButton: false,
                showCloseButton: true,
                width: '500px'
            });
        } else {
            alert('Template System requires SweetAlert2');
        }
    }

    /**
     * Load template
     */
    loadTemplate(templateType) {
        const templates = {
            weapon: {
                itemId: 100,
                upgrade: 15,
                extreme: 7,
                divine: 0,
                slot1: 'ATK+1',
                slot2: 'CRIT+1',
                slot3: 'NOT'
            },
            armor: {
                itemId: 200,
                upgrade: 15,
                extreme: 7,
                divine: 0,
                slot1: 'DEF+1',
                slot2: 'HP+10',
                slot3: 'NOT'
            },
            accessory: {
                itemId: 300,
                upgrade: 15,
                extreme: 7,
                divine: 0,
                slot1: 'STR+1',
                slot2: 'DEX+1',
                slot3: 'INT+1'
            }
        };

        const template = templates[templateType];
        if (template) {
            console.log(`Loading ${templateType} template:`, template);
            this.showNotification(`โหลดเทมเพลต ${templateType} แล้ว`, 'success');

            // Close template dialog
            if (typeof Swal !== 'undefined') {
                Swal.close();
            }
        }
    }

    /**
     * Save current as template
     */
    saveCurrentAsTemplate() {
        if (typeof Swal !== 'undefined') {
            Swal.fire({
                title: 'Save Template',
                input: 'text',
                inputLabel: 'Template Name:',
                inputPlaceholder: 'Enter template name...',
                showCancelButton: true,
                confirmButtonText: 'Save',
                cancelButtonText: 'Cancel'
            }).then((result) => {
                if (result.isConfirmed && result.value) {
                    console.log(`Saving template: ${result.value}`);
                    this.showNotification(`บันทึกเทมเพลต "${result.value}" แล้ว`, 'success');
                }
            });
        }
    }

    /**
     * Detect item type from item name
     */
    detectItemType(itemName) {
        if (!itemName) return 'Weapon'; // Default

        for (const [type, pattern] of Object.entries(this.itemTypePatterns)) {
            if (pattern.test(itemName)) {
                return type;
            }
        }

        return 'Weapon'; // Default fallback
    }

    /**
     * Get available slot options for item type
     */
    getSlotOptions(itemType) {
        const itemData = this.itemMapName[itemType] || this.itemMapName['Weapon'];
        // Extract option names from the dictionary structure
        return itemData.map(option => Object.values(option)[0]);
    }

    /**
     * Get slot option name by hex key
     */
    getSlotOptionByHex(itemType, hexKey) {
        const itemData = this.itemMapName[itemType] || this.itemMapName['Weapon'];
        const option = itemData.find(opt => Object.keys(opt)[0] === hexKey.toString());
        return option ? Object.values(option)[0] : 'NOT';
    }

    /**
     * Get hex key by slot option name
     */
    getHexBySlotOption(itemType, optionName) {
        const itemData = this.itemMapName[itemType] || this.itemMapName['Weapon'];
        const option = itemData.find(opt => Object.values(opt)[0] === optionName);
        return option ? Object.keys(option)[0] : '0';
    }

    /**
     * Convert slot option to hex value (updated for new structure)
     */
    getSlotHexValue(itemType, optionName) {
        if (optionName === 'NOT') {
            console.log(`🔍 getSlotHexValue: "${optionName}" → "0" (NOT option)`);
            return '0';
        }

        const hexValue = this.getHexBySlotOption(itemType, optionName);
        console.log(`🔍 getSlotHexValue: itemType="${itemType}", optionName="${optionName}" → hex="${hexValue}"`);

        // Debug: Show available options for this item type
        if (hexValue === '0' && this.itemMapName[itemType]) {
            console.log(`⚠️ Option "${optionName}" not found for ${itemType}. Available options:`,
                this.itemMapName[itemType].map(opt => Object.values(opt)[0]));
        }

        return hexValue;
    }

    /**
     * Calculate advanced options code with item type detection (ported from Python)
     */
    calculateAdvancedOptionsCode(itemData) {
        const {
            itemName = '',
            itemType = null,
            slot1 = 'NOT',
            slot2 = 'NOT',
            slot3 = 'NOT',
            craftOption = 'NOT',
            craftHeight = 0
        } = itemData;

        // Use provided item type or auto-detect from name
        const finalItemType = itemType || this.detectItemType(itemName);
        console.log(`🔍 calculateAdvancedOptionsCode: itemName="${itemName}", itemType="${finalItemType}"`);
        console.log(`🎯 Slots: slot1="${slot1}", slot2="${slot2}", slot3="${slot3}"`);
        console.log(`🎯 Craft: craftOption="${craftOption}", craftHeight="${craftHeight}" (type: ${typeof craftHeight})`);

        // Convert craftHeight to number if it's a string
        const craftHeightNum = parseInt(craftHeight) || 0;
        console.log(`🔧 Craft height converted: ${craftHeightNum}`);

        // Get item type data from itemMapName
        const itemTypeData = this.itemMapName[finalItemType];
        if (!itemTypeData) {
            console.log(`❌ No itemMapName data found for itemType: ${finalItemType}`);
            return {
                optionsCode: 0,
                itemType: finalItemType,
                slotDetails: {
                    slot1: { name: slot1, hex: '0', decimal: 0 },
                    slot2: { name: slot2, hex: '0', decimal: 0 },
                    slot3: { name: slot3, hex: '0', decimal: 0 }
                }
            };
        }

        // Function to get hex key for option name (ported from Python logic)
        const getOptionHex = (optionName) => {
            if (optionName === 'NOT') return '0';

            // Find the option in itemTypeData
            for (const optionObj of itemTypeData) {
                const optionValue = Object.values(optionObj)[0];
                if (optionValue === optionName) {
                    return Object.keys(optionObj)[0];
                }
            }
            return '0'; // Not found
        };

        // Get hex values for each slot
        const hex1 = getOptionHex(slot1);
        const hex2 = getOptionHex(slot2);
        const hex3 = getOptionHex(slot3);

        // Calculate craft option hex (ported from Python exactly)
        let craftHex = '00';
        if (craftOption !== 'NOT' && craftOption !== 'EMPTY' && craftHeightNum > 0) {
            const craftOptionHex = getOptionHex(craftOption);
            if (craftOptionHex !== '0') {
                // Convert craft height to hex based on Python logic
                // Python uses combo_craft_height.current() which is index (1-7 → 9-F)
                let craftHeightHex = '0';
                if (craftHeightNum === 1) craftHeightHex = '9';
                else if (craftHeightNum === 2) craftHeightHex = 'A';
                else if (craftHeightNum === 3) craftHeightHex = 'B';
                else if (craftHeightNum === 4) craftHeightHex = 'C';
                else if (craftHeightNum === 5) craftHeightHex = 'D';
                else if (craftHeightNum === 6) craftHeightHex = 'E';
                else if (craftHeightNum === 7) craftHeightHex = 'F';

                craftHex = craftHeightHex + craftOptionHex;
                console.log(`🔨 Craft: ${craftOption} (height:${craftHeightNum}) → ${craftHex}`);
            }
        }

        console.log(`🔢 Hex values: slot1="${hex1}", slot2="${hex2}", slot3="${hex3}", craft="${craftHex}"`);

        // Calculate options using Python algorithm exactly
        let slotsNumber = 0;
        let allOptions = '';

        if (slot1 !== 'NOT') {
            slotsNumber += 1;
            allOptions += hex1;
        }
        if (slot2 !== 'NOT') {
            slotsNumber += 1;
            allOptions += hex2;
        }
        if (slot3 !== 'NOT') {
            slotsNumber += 1;
            allOptions += hex3;
        }

        console.log(`📊 Slots number: ${slotsNumber}, All options: "${allOptions}"`);

        // Process options string (ported from Python algorithm)
        let optionCount = '';
        const allOptionsArray = allOptions.split('');
        let i = 0;

        while (i < allOptionsArray.length) {
            if (allOptionsArray[i] === '0') {
                optionCount = '00' + optionCount;
            } else {
                let count = 0;
                const a = allOptionsArray[i];

                // Count occurrences
                for (let n = 0; n < allOptionsArray.length; n++) {
                    if (a === allOptionsArray[n]) {
                        count += 1;
                    }
                }

                // Replace all occurrences with '0'
                for (let j = 0; j < allOptionsArray.length; j++) {
                    if (allOptionsArray[j] === a) {
                        allOptionsArray[j] = '0';
                    }
                }

                optionCount += count.toString() + a;
            }
            i += 1;
        }

        // Pad option count (ported from Python)
        if (optionCount.length === 2) {
            optionCount = '0000' + optionCount;
        } else if (optionCount.length === 4) {
            optionCount = '00' + optionCount;
        } else if (optionCount.length === 8) {
            optionCount = optionCount.substring(2);
        }

        console.log(`🔢 Option count: "${optionCount}"`);

        // Calculate final options code (ported from Python)
        let optionsCode = optionCount;

        if (slotsNumber === 0) {
            if (craftHex !== '00') {
                optionsCode = '000000' + craftHex;
            } else {
                optionsCode = '00000000';
            }
        } else if (slotsNumber === 1) {
            optionsCode = optionCount;
            if (craftHex !== '00') {
                optionsCode = '1' + optionsCode.substring(1) + craftHex;
            } else {
                optionsCode = '10' + optionsCode;
            }
        } else if (slotsNumber === 2) {
            if (craftHex !== '00') {
                optionsCode = '2' + optionsCode.substring(1) + craftHex;
            } else {
                optionsCode = '20' + optionsCode;
            }
        } else if (slotsNumber === 3) {
            if (craftHex !== '00') {
                optionsCode = '3' + optionsCode.substring(1) + craftHex;
            } else {
                optionsCode = '30' + optionsCode;
            }
        }

        console.log(`🎯 Final options code (hex): "${optionsCode}"`);

        // Convert to decimal
        const finalOptionsCode = parseInt(optionsCode, 16);

        console.log(`🎉 Final optionsCode: ${finalOptionsCode} (Hex: ${optionsCode})`);

        return {
            optionsCode: finalOptionsCode,
            itemType: finalItemType,
            craftCode: craftHex,
            slotDetails: {
                slot1: { name: slot1, hex: hex1, decimal: parseInt(hex1, 16) || 0 },
                slot2: { name: slot2, hex: hex2, decimal: parseInt(hex2, 16) || 0 },
                slot3: { name: slot3, hex: hex3, decimal: parseInt(hex3, 16) || 0 },
                craft: { name: craftOption, hex: craftHex, height: craftHeight }
            }
        };
    }

    /**
     * Show item type analyzer dialog
     */
    showItemTypeAnalyzer() {
        if (typeof Swal !== 'undefined') {
            Swal.fire({
                title: 'Item Type Analyzer',
                html: `
                    <div class="text-left">
                        <div class="form-group">
                            <label>Item Name:</label>
                            <input type="text" id="analyzerItemName" class="form-control" placeholder="Enter item name...">
                        </div>
                        <div class="form-group mt-3">
                            <label>Detected Type:</label>
                            <input type="text" id="analyzerDetectedType" class="form-control" readonly>
                        </div>
                        <div class="form-group mt-3">
                            <label>Available Slot Options:</label>
                            <div id="analyzerSlotOptions" class="mt-2">
                                <!-- Options will be populated here -->
                            </div>
                        </div>
                    </div>
                `,
                width: '600px',
                showConfirmButton: false,
                showCloseButton: true,
                didOpen: () => {
                    const nameInput = document.getElementById('analyzerItemName');
                    const typeInput = document.getElementById('analyzerDetectedType');
                    const optionsDiv = document.getElementById('analyzerSlotOptions');

                    nameInput.addEventListener('input', () => {
                        const itemName = nameInput.value;
                        const detectedType = this.detectItemType(itemName);

                        typeInput.value = detectedType;

                        // Display options with hex keys
                        const itemTypeData = this.itemMapName[detectedType] || [];
                        optionsDiv.innerHTML = itemTypeData.map(optionObj => {
                            const hexKey = Object.keys(optionObj)[0];
                            const optionName = Object.values(optionObj)[0];
                            return `<span class="badge badge-info mr-1 mb-1">${hexKey}: ${optionName}</span>`;
                        }).join('');
                    });
                }
            });
        }
    }
}

// Initialize when DOM is ready
document.addEventListener('DOMContentLoaded', () => {
    window.itemManager = new ItemManager();
});
