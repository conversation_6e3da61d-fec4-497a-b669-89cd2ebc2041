<?php

/*
 * Developed by FDEV
 * Copyright 2015
 * 
 * userLogin (CLASS)
 * controller file to userLogin (user logged in)
 */

class userLogged {

    public function exitHome() {
        if (isset($_GET['action']) == 'logout') {
            session_destroy();
            header('Location: index.php?action=logout');
        }
    }

    /*
     * @desc Get account info from user logged
     * */

    public function recUserAccount($column, $connection) {
        if (isset($_SESSION['userLogin'])) {
			$sessionUser = $_SESSION['userLogin'];
		} else {
			die("Error: ไม่มีค่า 'userLogin' ใน session");
		}
        $selectUserLogged = "SELECT * FROM [".DATABASE_ACC."].[dbo].cabal_auth_table WHERE Email = '$sessionUser' OR ID = '$sessionUser'";
        $selectUserLoggedParam = array();
        $selectUserLoggedQuery = sqlsrv_query($connection, $selectUserLogged, $selectUserLoggedParam);

        while ($resUserAcc = sqlsrv_fetch_array($selectUserLoggedQuery, SQLSRV_FETCH_ASSOC)) {
            $var = $resUserAcc[$column];
            return $var;
        }
    }
	
	public function recAllUserAccount($column, $connection, $usernum) {
		$query = "SELECT * FROM [".DATABASE_ACC."].[dbo].cabal_auth_table WHERE UserNum = ?";
		$params = array($usernum);
		$stmt = sqlsrv_query($connection, $query, $params);
		if ($stmt === false) {
			throw new Exception("Error: Query execution failed - " . print_r(sqlsrv_errors(), true));
		}
		$resUserAcc = sqlsrv_fetch_array($stmt, SQLSRV_FETCH_ASSOC);
		return $resUserAcc[$column] ?? null;
	}


////WEB_User_Info datatables////
    public function recUserInfo($column, $connection) {
        $userID = $this->recUserAccount('UserNum', $connection);
        $selectUserInfo = "SELECT * FROM WEB_User_Info WHERE UserNum = '$userID'";
        $selectUserInfoParam = array();
        $selectUserInfoQuery = sqlsrv_query($connection, $selectUserInfo, $selectUserInfoParam);

        while ($resUserInfo = sqlsrv_fetch_array($selectUserInfoQuery, SQLSRV_FETCH_ASSOC)) {
            $var = $resUserInfo[$column];
            return $var;
        }
    }
////cabal_warehouse_table datatables////
    public function recUserWH($column,$connection){
        $CustomerID = $this->recUserAccount('UserNum', $connection);
        $selectUserGame = "SELECT * FROM [".DATABASE_SV."].[dbo].cabal_warehouse_table WHERE UserNum = '$CustomerID'";
        $selectUserGameParam = array();
        $selectUserGameQuery = sqlsrv_query($connection, $selectUserGame, $selectUserGameParam);

        while ($resUserGame = sqlsrv_fetch_array($selectUserGameQuery, SQLSRV_FETCH_ASSOC)) {
            $var = $resUserGame[$column];
            return $var;
        }
    }
////cabal_charge_auth datatables////
    public function recUserGameInfo($column, $connection) {
        $CustomerID = $this->recUserAccount('UserNum', $connection);

        $selectUserGame = "SELECT * FROM [".DATABASE_ACC."].[dbo].cabal_charge_auth WHERE UserNum = '$CustomerID'";
        $selectUserGameParam = array();
        $selectUserGameQuery = sqlsrv_query($connection, $selectUserGame, $selectUserGameParam);

        while ($resUserGame = sqlsrv_fetch_array($selectUserGameQuery, SQLSRV_FETCH_ASSOC)) {
            $var = $resUserGame[$column];
            return $var;
        }
    }
////CashAccount datatables////
	public function recUserGameCash($column, $connection) {
        $CustomerID = $this->recUserAccount('UserNum', $connection);

        $selectUserGameCash = "SELECT * FROM  [".DATABASE_CCA."].[dbo].CashAccount WHERE UserNum = '$CustomerID'";
        $selectUserGameCashParam = array();
        $selectUserGameCashQuery = sqlsrv_query($connection, $selectUserGameCash, $selectUserGameCashParam);

        while ($resUserGameCash = sqlsrv_fetch_array($selectUserGameCashQuery, SQLSRV_FETCH_ASSOC)) {
            $var = $resUserGameCash[$column];
            return $var;
        }
    }
 ////TPoint datatables////
    public function recUserTpoint($column, $connection) {

        $CustomerID = $this->recUserAccount('UserNum', $connection);

        $selectUserGameTp = "SELECT * FROM  [".DATABASE_NBL."].[dbo].Point WHERE UserNum = '$CustomerID'";
        $selectUserGameTpParam = array();
        $selectUserGameTpQuery = sqlsrv_query($connection, $selectUserGameTp, $selectUserGameTpParam);
        while ($resUserGameTp = sqlsrv_fetch_array($selectUserGameTpQuery, SQLSRV_FETCH_ASSOC)) {
            $var = $resUserGameTp[$column];
            return $var;
        }
    }
////mail datatables////
    function mailre_count($connection, $user, $status = null){
        {
           // $cond = ($status ? "WHERE ReceiverCharIdx = '$Charid' AND State = '$status'" : "WHERE ReceiverCharIdx = '$user'");
            $selectTickets = "SELECT * FROM [".DATABASE_SV."].[dbo].cabal_mail_received_table WHERE ReceiverCharIdx/16 = '$user' AND State = '$status'";
            $selectTicketsOpt = array("Scrollable" => SQLSRV_CURSOR_KEYSET);
            $selectTicketsQuery = sqlsrv_query($connection, $selectTickets, array(), $selectTicketsOpt);
            $selectTicketsRows = sqlsrv_num_rows($selectTicketsQuery);
            
            if($selectTicketsRows){
                return $selectTicketsRows;
            }else{
                return false;
            }
        }
    }
////Character datatables////
    public function recUserChars($connection, $column = null) {
        $CustomerID = $this->recUserAccount('UserNum', $connection);

        $selectUserChars = "SELECT * FROM [".DATABASE_SV."].[dbo].cabal_character_table WHERE CharacterIdx/16 = '$CustomerID'";
        $selectUserCharsParam = array();
        $selectUserCharsOption = array("Scrollable" => SQLSRV_CURSOR_KEYSET);
        $selectUserCharsQuery = sqlsrv_query($connection, $selectUserChars, $selectUserCharsParam, $selectUserCharsOption);
        $selectNumRowsChars = sqlsrv_num_rows($selectUserCharsQuery);
        $resUserCharsSingle = sqlsrv_fetch_array($selectUserCharsQuery, SQLSRV_FETCH_ASSOC);

        if ($column == null || !$column) {
            // if not specify a column the function returns quantity of items
            return $selectNumRowsChars;
        }

        while ($resUserChars = sqlsrv_fetch_array($selectUserCharsQuery, SQLSRV_FETCH_ASSOC)) {
            $var = $resUserChars[$column];
            return $var;
        }
    }
////account datatables////
public function CheckUserAccount($unum,$column, $connection) {

        $selectUserAcc= "SELECT * FROM [".DATABASE_ACC."].[dbo].cabal_auth_table WHERE  UserNum = '$unum'";
        $selectUserAccParam = array();
        $selectUserAccQuery = sqlsrv_query($connection, $selectUserAcc, $selectUserAccParam);

        while ($resUserAccs = sqlsrv_fetch_array($selectUserAccQuery, SQLSRV_FETCH_ASSOC)) {
            $var = $resUserAccs[$column];
            return $var;
        }
    }
////Character datatables////
    public function recCharecter($chareter, $column, $connection) {

        $selectCharecter = "SELECT * FROM  [".DATABASE_SV."].[dbo].cabal_character_table WHERE CharacterIdx = '$chareter'";
        $selectCharecterParam = array();
        $selectCharecterQuery = sqlsrv_query($connection, $selectCharecter, $selectCharecterParam);

        while ($resCharecter = sqlsrv_fetch_array($selectCharecterQuery, SQLSRV_FETCH_ASSOC)) {
            $var = $resCharecter[$column];
            return $var;
        }
    }
////Guild datatables////
    public function recGuild($GuildNo,$column, $connection) {

        $selectGuild= "SELECT * FROM  [".DATABASE_SV."].[dbo].Guild WHERE GuildNo = '$GuildNo'";
        $selectGuildParam = array();
        $selectGuildQuery = sqlsrv_query($connection, $selectGuild, $selectGuildParam);

        while ($resGuild = sqlsrv_fetch_array($selectGuildQuery, SQLSRV_FETCH_ASSOC)) {
            $var = $resGuild[$column];
            return $var;
        }
    }
////Guild datatables////
    public function recGuildCharecter($chareter, $column, $connection) {

        $selectGuildCharecter = "SELECT * FROM  [".DATABASE_SV."].[dbo].GuildMember WHERE CharacterIndex = '$chareter'";
        $selectGuildCharecterParam = array();
        $selectGuildCharecterQuery = sqlsrv_query($connection, $selectGuildCharecter, $selectGuildCharecterParam);

        while ($resGuildCharecter = sqlsrv_fetch_array($selectGuildCharecterQuery, SQLSRV_FETCH_ASSOC)) {
            $var = $resGuildCharecter[$column];
            return $var;
        }
    }
////Guild datatables////
public function recForcecalibur($CharacterIdx,$column, $connection) {

    $selectForcecalibur = "SELECT * FROM  [".DATABASE_SV."].[dbo].cabal_Forcecalibur_Owner WHERE CharacterIdx = '$CharacterIdx'";
    $selectForcecaliburParam = array();
    $selectForcecaliburQuery = sqlsrv_query($connection, $selectForcecalibur, $selectForcecaliburParam);

    while ($resForcecalibur = sqlsrv_fetch_array($selectForcecaliburQuery, SQLSRV_FETCH_ASSOC)) {
        $var = $resForcecalibur[$column];
        return $var;
    }
}
////count cabal_auth_table datatables////
    public function countTotalReferral($conn) {
        $CustomerID = $this->recUserAccount('UserNum', $conn);
        $countReferral = "SELECT * FROM [".DATABASE_ACC."].[dbo].cabal_auth_table WHERE ReferralID = '$CustomerID'";
        $countReferralOpt = array("Scrollable" => SQLSRV_CURSOR_KEYSET);
        $countReferralQuery = sqlsrv_query($conn, $countReferral, array(), $countReferralOpt);

        return sqlsrv_num_rows($countReferralQuery);
    }
    
    public function recUserPerm($connection, $column, $permType){
        $CustomerID = $this->recUserAccount('UserNum', $connection);
        
        if($permType == 'menu'):
            $selectMenu = "SELECT * FROM WEB_Perm_Menus WHERE UserNum = '$CustomerID'";
            $selectMenuParam = array();
            $selectMenuQuery = sqlsrv_query($connection, $selectMenu, $selectMenuParam);
            
            while($resMenuPerm = sqlsrv_fetch_array($selectMenuQuery, SQLSRV_FETCH_ASSOC)):
                $name = $resMenuPerm[$column];
                return $name;
            endwhile;
            
        elseif($permType == 'extra'):
            $selectExtra = "SELECT * FROM WEB_Perm_Extras WHERE UserNum = '$CustomerID'";
            $selectExtraParam = array();
            $selectExtraQuery = sqlsrv_query($connection, $selectExtra, $selectExtraParam);
            
            while($resExtraPerm = sqlsrv_fetch_array($selectExtraQuery, SQLSRV_FETCH_ASSOC)):
                $name = $resExtraPerm[$column];
                return $name;
            endwhile;
        else:
            return false;
        endif;
    }

public function nation($nation) {
    $nation_name = array(0 => 'NA', 1 => 'Capella', 2 => 'Procyon', 3 => 'GM');

    // ตรวจสอบว่าดัชนีที่กำหนดมีอยู่ในอาร์เรย์หรือไม่
    if (array_key_exists($nation, $nation_name)) {
        return $nation_name[$nation];
    } else {
        return 'Unknown'; // หรือค่าที่คุณต้องการสำหรับการดัชนีที่ไม่ถูกต้อง
    }
}
    public function utf8_to_thai($string) {
        return mb_convert_encoding($string, 'UTF-16', 'UTF-8');
    }
    public function utf8_to_thai2($string) {
        return iconv( 'windows-874', 'UTF-8',$string);
       }    
    
    public function Reputation($string) {
       if ($string<=10000) { echo '1'; }
       if ($string>=10000 and $string<20000) { echo '1'; }
       if ($string>=20000 and $string<40000) { echo '2'; }
       if ($string>=40000 and $string<80000) { echo '3'; }
       if ($string>=80000 and $string<160000) { echo '4'; }
       if ($string>=160000 and $string<320000) { echo '5'; }
       if ($string>=320000 and $string<640000) { echo '6'; }
       if ($string>=640000 and $string<1280000) { echo '7'; }
       if ($string>=1280000 and $string<2560000) { echo '8'; }
       if ($string>=2560000 and $string<5120000) { echo '9'; }
       if ($string>=5120000 and $string<10000000) { echo '10'; }
       if ($string>=10000000 and $string<20000000) { echo '11'; }
       if ($string>=20000000 and $string<50000000) { echo '12'; }
       if ($string>=50000000 and $string<80000000) { echo '13'; }
       if ($string>=80000000 and $string<150000000) { echo '14'; }
       if ($string>=150000000 and $string<300000000) { echo '15'; }
       if ($string>=300000000 and $string<500000000) { echo '16'; }
       if ($string>=500000000 and $string<1000000000) { echo '17'; }
       if ($string>=1000000000 and $string<1500000000) { echo '18'; }
       if ($string>=1500000000 and $string<2000000000) { echo '19'; }
       if ($string>=2000000000) { echo '20'; }
    }
    public function alzclass($string){
    $il = strlen($string); 
    if($il >= 1 AND $il < 4){echo ''.number_format($string).''; }
    if($il >= 4 AND $il < 7){echo ''.number_format(floor($string/1000)).' K'; }
    if($il >= 7 AND $il < 10){echo ''.number_format(floor($string/1000000)).' M'; }
    if($il >= 10){echo ''.number_format(floor($string/1000000000)).' KM'; }
    }
    public function statustrue($string){
    if($string == 0){echo '<span class="label label-default">0</span>'; }
    if($string == 1){echo '<span class="label label-success">50</span>'; }
    if($string == 2){echo '<span class="label label-success">90</span>'; }
    if($string == 3){echo '<span class="label label-success">150</span>'; }
    if($string == 4){echo '<span class="label label-success">300</span>'; }
    if($string == 5){echo '<span class="label label-success">500</span>'; }
    if($string == 6){echo '<span class="label label-success">1000</span>'; }
    }
    public function statustruscard($string){
    if($string == 0){echo '<span class="label label-default">รอการตรวจสอบ...</span>'; }
    if($string == 1){echo '<span class="label label-success">ผ่าน</label>'; }
    if($string == 3){echo '<span class="label label-danger">ถูกใช้ไปแล้ว</label>'; }
    if($string == 4){echo '<span class="label label-danger">รหัสไม่ถูกต้อง</label>'; }
    if($string == 5){echo '<span class="label label-danger">บัตรทรูมูฟ</label>'; }
    }
    public function thaitrans($string){
    $name2 =  mb_convert_encoding($string, 'UTF-16', 'UTF-8');
    $name3 =  iconv('windows-874', 'UTF-8',$name2);
    return $name3;
    }
    public function thaitrans2($string){
        $name2 =  mb_convert_encoding($string, 'UTF-8', 'UTF-16');
        $name3 =  iconv('UTF-8', 'windows-874',$name2);
        return $name3;
    }
    
    public function chartowar($txtchaid) { 
        $real = ($txtchaid + 0xBC614E) ^ 0x63783;
        $tmp1 = round($real / 100000);
        $tmp2 = round(($real - $tmp1 * 1000000 ) / 100);
        $tmp3 = round($real - $tmp1 * 100000 - $tmp2 * 100);
        $waridx = substr($real,6,2).substr($real,0,4).substr($real,4,2);
       return $waridx; 
    } 
    public function wartochar($txtwarid) { 
        $tmp1 = round($txtwarid / 1000000); 
        $tmp2 = round(($txtwarid - $tmp1 * 1000000 ) / 100); 
        $tmp3 = round( $txtwarid - $tmp1 * 1000000 - $tmp2 * 100); 
        $tmpID = $tmp2 * 10000 + $tmp3 * 100 + $tmp1; 
        $charID = ($tmpID  ^ 0x63783) - 0xBC614E; 
        return  $charID; 
    }

	public function worldnameidx($string) {
		$worlds = array(
		1 => "Bloody Ice",
		2 => "Desert Scream",
		3 => "Green Despair",
		4 => "Port Lux",
		5 => "Fort. Ruina",
		6 => "Lakeside",
		7 => "Undead Ground",
		8 => "Forgotten Ruin",
		9 => "Mutant Forest",
		10 => "Pontus Ferrum",
		11 => "Porta Inferno",
		12 => "Arcane Trace",
		13 => "Forest of Nostalgia",
		14 => "Art of War",
		15 => "Mission War",
		16 => "Lobby War",
		17 => "Senilinea",
		18 => "OTAM",
		19 => "Jail",
		20 => "Chaos Arena",
		21 => "Tierra del Bruto",
		22 => "Tower of the Dead B3F",
		25 => "Exilian Volcano",
		27 => "Dungeon World 3",
		28 => "Dungeon World 2",
		29 => "Dungeon World 1",
		30 => "Warp Center",
		31 => "LBS",
		32 => "Forgotten Temple B2F",
		33 => "Forbidden Island",
		34 => "Altar of Siena B1F",
		35 => "Altar of Siena B2F",
		36 => "Panic Cave (Easy)",
		37 => "Panic Cave (Normal)",
		38 => "Panic Cave (Hard)",
		39 => "Weakened Lake in Dusk",
		40 => "Weakened Ruina Station",
		41 => "Weakened Frozen Tower of Undead B1F",
		42 => "Steamer Crazy (Easy)",
		43 => "Steamer Crazy (Normal)",
		44 => "Steamer Crazy (Hard)",
		45 => "Illusion Castle Underworld",
		46 => "Catacomb Frost (Easy)",
		47 => "Catacomb Frost (Normal)",
		48 => "Catacomb Frost (Hard)",
		49 => "Illusion Castle Radiant Hall",
		50 => "Abandoned City",
		51 => "Forbidden Island (Awakened)",
		52 => "Tower of the Dead B3F (Part2)",
		53 => "Legend Arena",
		54 => "Chaos Arena Lv.5",
		55 => "Chaos Arena Lv.6",
		56 => "Panic Cave (Premium)",
		57 => "Steamer Crazy (Premium)",
		58 => "Catacomb Frost (Premium)",
		59 => "Glacies Inferna",
		60 => "Lava Hellfire (Easy)",
		61 => "Lava Hellfire (Normal)",
		62 => "Lava Hellfire (Hard)",
		63 => "Lava Hellfire (Premium)",
		64 => "Bloody Ice",
		65 => "Unknown Maze",
		66 => "Fort. Ruina",
		67 => "Desert Scream",
		68 => "Maquinas Outpost",
		69 => "Warp Center",
		70 => "Green Despair",
		71 => "Port Lux",
		72 => "Forgotten Ruin",
		73 => "Lakeside",
		74 => "Hazardous Valley (Easy)",
		75 => "Hazardous Valley (Normal)",
		76 => "Hazardous Valley (Hard)",
		77 => "Chaos Infinity",
		78 => "Frozen Colosseum",
		79 => "Forgotten Temple B2F (Awakened)",
		80 => "Lava Hellfire (Awakened)",
		81 => "Panic Cave (Awakened)",
		82 => "Steamer Crazy (Awakened)",
		83 => "Catacomb Frost (Awakened)",
		84 => "Hazardous Valley (Awakened)",
		85 => "Dungeon World 4",
		86 => "Illusion Castle Underworld(Apocrypha)",
		87 => "Illusion Castle Radiant Hall(Apocrypha)",
		88 => "Edge of Phantom",
		89 => "Forgotten Temple B3F",
		90 => "Acheron Arena",
		91 => "Devil's Tower",
		92 => "Devil's Tower (Part2)",
		93 => "Pandemonium",
		94 => "Mirage Island",
		95 => "Chaos Arena Lv.7",
		96 => "Forest of Nostalgia",
		97 => "Flame Dimension",
		98 => "Flame Nest",
		99 => "Holiday Windhill",
		100 => "Labyrinth",
		101 => "Ancient Tomb",
		102 => "Dimensional clearance",
		103 => "Frozen Canyon",
		104 => "Holia Keldrasil",
		105 => "Tower of the Dead B4F",
		106 => "Tower of the Dead B5F",
		107 => "",
		109 => "Terminus Machina",
		110 => "Garden Of Dust",
		111 => "Secret Base SCA-7A",
		112 => "Ruina Station",
		113 => "Forgotten Temple B1F",
		114 => "The Volcanic Citadel",
		115 => "Lake in Dusk"
		);
		if (isset($worlds[$string])) {
			return $worlds[$string];
		} else {
			return 'Unknown World'; // หรือค่าเริ่มต้นอื่น ๆ ที่คุณต้องการ
		}
	}
    public function worldnameidxdd($string) {    
		if($string == 1){$world = 'Bloody Ice'; }
		if($string == 2){$world = 'Desert Scream'; }
		if($string == 3){$world = 'Green Despair'; }
		if($string == 4){$world = 'Port Lux'; }
		if($string == 5){$world = 'Fort. Ruina'; }
		if($string == 6){$world = 'Lakeside'; }
		if($string == 7){$world = 'Undead Ground'; }
		if($string == 8){$world = 'Forgotten Ruin'; }
		if($string == 9){$world = 'Mutant Forest'; }
		if($string == 10){$world = 'Pontus Ferrum'; }
		if($string == 11){$world = 'Porta Inferno'; }
		if($string == 12){$world = 'Arcane Trace'; }
		if($string == 13){$world = 'Frozen Tower Of Undead'; }
		if($string == 14){$world = 'Ruina Station'; }
		if($string == 15){$world = 'MAPHEL'; }
		if($string == 16){$world = 'AUTRA'; }
		if($string == 17){$world = 'Forbidden Island (ยากมาก)'; }
		if($string == 18){$world = 'Forgotten Temple B2F (ยากมาก)'; }
		if($string == 19){$world = 'Jail'; }
		if($string == 20){$world = 'Chrysos'; }
		if($string == 21){$world = 'Senillinia'; }
		if($string == 22){$world = 'Tower Of the Dead B3F'; }
		if($string == 23){$world = 'Forgotten Temple B1F'; }
		if($string == 24){$world = 'The Volcanic Citadel'; }
		if($string == 25){$world = 'Exilian Volcano'; }
		if($string == 26){$world = 'Lake in Dusk'; }
		if($string == 27){$world = 'Dungeon World 3'; }
		if($string == 28){$world = 'Dungeon World 2'; }
		if($string == 29){$world = 'Dungeon World 1'; }
		if($string == 30){$world = 'Warp Center'; }
		if($string == 31){$world = 'LBS'; }
		if($string == 32){$world = 'The Frozen Tower Of Undead(B1F)'; }
		if($string == 33){$world = 'The Frozen Tower Of Undead(B2F)'; }
		if($string == 34){$world = 'Forgotten Temple B2F'; }
		if($string == 35){$world = 'Forbidden Island'; }
		if($string == 36){$world = 'Altar Of Siena B1F'; }
		if($string == 37){$world = 'Altar Of Siena B2F'; }
		if($string == 38){$world = 'Panic Cave (ง่าย)'; }
		if($string == 39){$world = 'Panic Cave (Normal)'; }
		if($string == 40){$world = 'Panic Cave (ยาก)'; }
		if($string == 41){$world = 'Castle Of Illusions (ยากมาก)'; }
		if($string == 42){$world = 'Radiant Hall Of the Castle Of Illusions (ยากมาก)'; }
		if($string == 43){$world = 'Forgotten Temple B3F'; }
		if($string == 44){$world = 'Steamer Crazy (ง่าย)'; }
		if($string == 45){$world = 'Steamer Crazy (Normal)'; }
		if($string == 46){$world = 'Steamer Crazy (ยาก)'; }
		if($string == 47){$world = 'Illusion Castle Underworld'; }
		if($string == 48){$world = 'Catacomb Frost (ง่าย)'; }
		if($string == 49){$world = 'Catacomb Frost (Normal)'; }
		if($string == 50){$world = 'Catacomb Frost (ยาก)'; }
		if($string == 51){$world = 'Illusion Castle Radiant Hall'; }
		if($string == 52){$world = 'Chaos Arena Lv. 1'; }
		if($string == 53){$world = 'Chaos Arena Lv. 2'; }
		if($string == 54){$world = 'Chaos Arena Lv. 3'; }
		if($string == 55){$world = 'Chaos Arena Lv. 4'; }
		if($string == 56){$world = 'Chaos Arena Lv. 5'; }
		if($string == 57){$world = 'Chaos Arena Lv. 6'; }
		if($string == 58){$world = 'Panic Cave (พรีเมี่ยม)'; }
		if($string == 59){$world = 'Steamer Crazy (พรีเมี่ยม)'; }
		if($string == 60){$world = 'Catacomb Frost (พรีเมี่ยม)'; }
		if($string == 61){$world = 'Arena Defense'; }
		if($string == 62){$world = 'Lava Hellfire (ง่าย)'; }
		if($string == 63){$world = 'Lava Hellfire (Normal)'; }
		if($string == 64){$world = 'Lava Hellfire (ยาก)'; }
		if($string == 65){$world = 'Lava Hellfire (พรีเมี่ยม)'; }
		if($string == 66){$world = 'Bloody Ice'; }
		if($string == 67){$world = 'Unknown Maze'; }
		if($string == 68){$world = 'Fort. Ruina'; }
		if($string == 69){$world = 'Desert Scream'; }
		if($string == 70){$world = 'Maquinas Outpost'; }
		if($string == 71){$world = 'Warp Center'; }
		if($string == 72){$world = 'Green Despair'; }
		if($string == 73){$world = 'Tower Of the Dead B3F (2 part)'; }
		if($string == 74){$world = 'Nostalgic Forest'; }
		if($string == 75){$world = 'Forgotten Temple B2F (ยากมาก)'; }
		if($string == 76){$world = 'Hazardous Valley (ง่าย)'; }
		if($string == 77){$world = 'Hazardous Valley (Normal)'; }
		if($string == 78){$world = 'Hazardous Valley (ยาก)'; }
		if($string == 79){$world = 'Chaos Infinity'; }
		if($string == 80){$world = 'Insane Altar'; }
		if($string == 81){$world = 'Hazardous Valley (ยากมาก)'; }
		if($string == 82){$world = 'Aizhan and Elena s Havoc'; }
		if($string == 83){$world = 'Frozen Coliseum'; }
		if($string == 84){$world = 'PVP Arena'; }
		if($string == 85){$world = 'Event Arena'; }
		if($string == 86){$world = 'Panic Cave (ยากมาก)'; }
		if($string == 87){$world = 'Legendary Arena'; }
		if($string == 88){$world = 'Glacies Inferna'; }
		if($string == 89){$world = 'Ghost Hill'; }
		if($string == 90){$world = 'Arena Of Acheron'; }
		if($string == 91){$world = 'Maligian Tower'; }
		if($string == 92){$world = 'Eternal Chaos Arena'; }
		if($string == 93){$world = 'Weakened Twilight Lake'; }
		if($string == 94){$world = 'Weakened Ruin Station'; }
		if($string == 95){$world = 'Weakened Tower Of the Dead B1F'; }
		if($string == 96){$world = 'Pandemonium'; }
		if($string == 97){$world = 'Island Mirage'; }
    }

	// public function cabalstyle($ClassType) { 
	// 		$color2 = "success";  // Default color

	// 		if ($ClassType == 1) {
	// 			$ClassType = "WA";
	// 		} else if ($ClassType == 2) {
	// 			$ClassType = "BL";
	// 		} else if ($ClassType == 3) {
	// 			$ClassType = "WI";
	// 		} else if ($ClassType == 4) {
	// 			$ClassType = "FA";
	// 			$color2 = "danger";
	// 		} else if ($ClassType == 5) {
	// 			$ClassType = "FS";
	// 		} else if ($ClassType == 6) {
	// 			$ClassType = "FB";
	// 		} else if ($ClassType == 7) {
	// 			$ClassType = "GL";
	// 		} else if ($ClassType == 8) {
	// 			$ClassType = "FG";
	// 		} else if ($ClassType == 9) {
	// 			$ClassType = "DM";
	// 		} else {
	// 			$ClassType = "NO";
	// 			$color2 = "default";  // You might want to set a default color for this case
	// 		}

	// 		// You can return the class type and color as needed
	// 		return array("ClassType" => $ClassType, "Color" => $color2);
	// }
public function cabalstylenum($style) {
    $class_array = array(
        1 => 'WA', // Warrior
        2 => 'BL', // Blader
        3 => 'WI', // Wizard
        4 => 'FA', // Force Archer
        5 => 'FS', // Force Shielder
        6 => 'FB', // Force Blader
        7 => 'GL', // Gladiator
        8 => 'FG', // Force Gunner
        9 => 'DM'  // Dark Mage
    );

    $result['Class_Name'] = isset($class_array[$style]) ? $class_array[$style] : 'Unknown';

    return $result;
}
    public function cabalstyle($style) {
        $class_array = array(
            1 => 'WA', // Warrior
            2 => 'BL', // Blader
            3 => 'WI', // Wizard
            4 => 'FA', // Force Archer
            5 => 'FS', // Force Shielder
            6 => 'FB', // Force Blader
            7 => 'GL', // Gladiator
            8 => 'FG', // Force Gunner
            9 => 'DM'  // Dark Mage
        );

        // ดึงค่า Battle Style จาก 3 บิตแรก
        $battleStyle = $style & 7; // (0b0111)

        // ดึงค่า Extended Battle Style จากบิตที่ 23
        $extendedBattleStyle = ($style >> 23) & 1;

        // รวมค่าเพื่อหาว่าเป็นอาชีพอะไร
        $classIndex = $battleStyle | ($extendedBattleStyle << 3);

        // ตรวจสอบว่าค่าที่ได้ตรงกับอาชีพจริงหรือไม่
        $result['Class'] = $classIndex;
        $result['Class_Name'] = isset($class_array[$classIndex]) ? $class_array[$classIndex] : 'Unknown';

        return $result;
    }
public function getItemDurations($durationIdx) {
        $itemDurations = [
        1 => ["Day" => 0, "Hour" => 1, "Min" => 0],
        2 => ["Day" => 0, "Hour" => 2, "Min" => 0],
        3 => ["Day" => 0, "Hour" => 3, "Min" => 0],
        4 => ["Day" => 0, "Hour" => 4, "Min" => 0],
        5 => ["Day" => 0, "Hour" => 5, "Min" => 0],
        6 => ["Day" => 0, "Hour" => 6, "Min" => 0],
        7 => ["Day" => 0, "Hour" => 10, "Min" => 0],
        8 => ["Day" => 0, "Hour" => 12, "Min" => 0],
        9 => ["Day" => 1, "Hour" => 0, "Min" => 0],
        10 => ["Day" => 3, "Hour" => 0, "Min" => 0],
        11 => ["Day" => 5, "Hour" => 0, "Min" => 0],
        12 => ["Day" => 7, "Hour" => 0, "Min" => 0],
        13 => ["Day" => 10, "Hour" => 0, "Min" => 0],
        14 => ["Day" => 14, "Hour" => 0, "Min" => 0],
        15 => ["Day" => 15, "Hour" => 0, "Min" => 0],
        16 => ["Day" => 20, "Hour" => 0, "Min" => 0],
        17 => ["Day" => 30, "Hour" => 0, "Min" => 0],
        18 => ["Day" => 45, "Hour" => 0, "Min" => 0],
        19 => ["Day" => 60, "Hour" => 0, "Min" => 0],
        20 => ["Day" => 90, "Hour" => 0, "Min" => 0],
        21 => ["Day" => 100, "Hour" => 0, "Min" => 0],
        22 => ["Day" => 120, "Hour" => 0, "Min" => 0],
        23 => ["Day" => 180, "Hour" => 0, "Min" => 0],
        24 => ["Day" => 270, "Hour" => 0, "Min" => 0],
        25 => ["Day" => 365, "Hour" => 0, "Min" => 0],
        26 => ["Day" => 0, "Hour" => 0, "Min" => 3],
        27 => ["Day" => 0, "Hour" => 0, "Min" => 30],
        28 => ["Day" => 0, "Hour" => 0, "Min" => 90],
        29 => ["Day" => 0, "Hour" => 0, "Min" => 10],
        30 => ["Day" => 0, "Hour" => 0, "Min" => 0],
        31 => ["Day" => 0, "Hour" => 0, "Min" => 0],
        ];

        return isset($itemDurations[$durationIdx]) ? $itemDurations[$durationIdx] : null;
    }


    public function countuser($conn = null){
        {
           // $cond = ($status ? "WHERE Login = '$status'" : "WHERE Login = '$status'");
            $selectTickets = "SELECT * FROM [".DATABASE_ACC."].[dbo].cabal_auth_table";
            $selectTicketsOpt = array("Scrollable" => SQLSRV_CURSOR_KEYSET);
            $selectTicketsQuery = sqlsrv_query($conn, $selectTickets, array(), $selectTicketsOpt);
            $selectTicketsRows = sqlsrv_num_rows($selectTicketsQuery);
            
            if($selectTicketsRows){
                return $selectTicketsRows;
            }else{
                return false;
            }
        }
        }
    public function countuserbanned($conn = null){
        {
           // $cond = ($status ? "WHERE Login = '$status'" : "WHERE Login = '$status'");
            $selectTickets = "SELECT * FROM [".DATABASE_ACC."].[dbo].cabal_auth_table Where AuthType = 2";
            $selectTicketsOpt = array("Scrollable" => SQLSRV_CURSOR_KEYSET);
            $selectTicketsQuery = sqlsrv_query($conn, $selectTickets, array(), $selectTicketsOpt);
            $selectTicketsRows = sqlsrv_num_rows($selectTicketsQuery);
            
            if($selectTicketsRows){
                return $selectTicketsRows;
            }else{
                return false;
            }
        }
        }
    public function countcharecter($conn = null){
            {
                //$cond = ($status ? "WHERE user_no = '$user' AND status > '$status' AND added_time > (GETDATE()-1)" : "WHERE user_no = '$user'");
                $selectTickets = "SELECT * FROM [".DATABASE_SV."].[dbo].cabal_character_table";
                $selectTicketsOpt = array("Scrollable" => SQLSRV_CURSOR_KEYSET);
                $selectTicketsQuery = sqlsrv_query($conn, $selectTickets, array(), $selectTicketsOpt);
                $selectTicketsRows = sqlsrv_num_rows($selectTicketsQuery);
                
                if($selectTicketsRows){
                    return $selectTicketsRows;
                }else{
                    return false;
                }
            }
            }
    public function countuseronline($conn,$status= null){
                {
                    $cond = ($status ? "WHERE Login = '$status'" : "WHERE Login = '$status'");
                    $selectTickets = "SELECT * FROM [".DATABASE_SV."].[dbo].cabal_character_table {$cond}";
                    $selectTicketsOpt = array("Scrollable" => SQLSRV_CURSOR_KEYSET);
                    $selectTicketsQuery = sqlsrv_query($conn, $selectTickets, array(), $selectTicketsOpt);
                    $selectTicketsRows = sqlsrv_num_rows($selectTicketsQuery);
                    
                    if($selectTicketsRows){
                        return $selectTicketsRows;
                    }else{
                        return false;
                    }
                }
                }
    public function countnation($conn,$status= null){
                    {
                        $cond = ($status ? "WHERE Nation = '$status'" : "WHERE Nation = '$status'");
                        $selectTickets = "SELECT * FROM [".DATABASE_SV."].[dbo].cabal_character_table {$cond}";
                        $selectTicketsOpt = array("Scrollable" => SQLSRV_CURSOR_KEYSET);
                        $selectTicketsQuery = sqlsrv_query($conn, $selectTickets, array(), $selectTicketsOpt);
                        $selectTicketsRows = sqlsrv_num_rows($selectTicketsQuery);
                        
                        if($selectTicketsRows){
                            return $selectTicketsRows;
                        }else{
                            return false;
                        }
                    }
                    }
    
    public function countguild($conn = null){
                    {
                        //$cond = ($status ? "WHERE user_no = '$user' AND status > '$status' AND added_time > (GETDATE()-1)" : "WHERE user_no = '$user'");
                        $selectTickets = "SELECT * FROM [".DATABASE_SV."].[dbo].Guild";
                        $selectTicketsOpt = array("Scrollable" => SQLSRV_CURSOR_KEYSET);
                        $selectTicketsQuery = sqlsrv_query($conn, $selectTickets, array(), $selectTicketsOpt);
                        $selectTicketsRows = sqlsrv_num_rows($selectTicketsQuery);
                        
                        if($selectTicketsRows){
                            return $selectTicketsRows;
                        }else{
                            return false;
                        }
                    }
                    }
        
    public function countuseronlineforchanell($conn,$channal,$status = null){
                {
                    $cond = ($status ? "WHERE Login = '$status' AND ChannelIdx = '$channal'" : "WHERE Login = '$status'");
                    $selectTickets = "SELECT * FROM [".DATABASE_SV."].[dbo].cabal_character_table {$cond}";
                    $selectTicketsOpt = array("Scrollable" => SQLSRV_CURSOR_KEYSET);
                    $selectTicketsQuery = sqlsrv_query($conn, $selectTickets, array(), $selectTicketsOpt);
                    $selectTicketsRows = sqlsrv_num_rows($selectTicketsQuery);
                    
                    if($selectTicketsRows){
                        return $selectTicketsRows;
                    }else{
                        return false;
                    }
                }
                }
    public function countip($conn,$status= null){
                    {
                        $cond = ($status ? "WHERE IP = '$status'" : "WHERE IP = '$status'");
                        $selectTickets = "SELECT * FROM [".DATABASE_ACC."].[dbo].cabal_auth_table {$cond}";
                        $selectTicketsOpt = array("Scrollable" => SQLSRV_CURSOR_KEYSET);
                        $selectTicketsQuery = sqlsrv_query($conn, $selectTickets, array(), $selectTicketsOpt);
                        $selectTicketsRows = sqlsrv_num_rows($selectTicketsQuery);
                        
                        if($selectTicketsRows){
                            return $selectTicketsRows;
                        }else{
                            return false;
                        }
                    }
                  }
    public function recShowimg($id, $column, $connection) {

                        $selectCharecter = "SELECT * FROM WEB_Promotion_show WHERE id = '$id'";
                        $selectCharecterParam = array();
                        $selectCharecterQuery = sqlsrv_query($connection, $selectCharecter, $selectCharecterParam);
                
                        while ($resCharecter = sqlsrv_fetch_array($selectCharecterQuery, SQLSRV_FETCH_ASSOC)) {
                            $var = $resCharecter[$column];
                            return $var;
                        }
                    }

             
}

?>